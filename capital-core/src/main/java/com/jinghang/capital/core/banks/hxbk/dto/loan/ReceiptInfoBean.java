package com.jinghang.capital.core.banks.hxbk.dto.loan;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.jinghang.capital.core.banks.hxbk.config.FlexibleLocalDateTimeDeserializer;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025-07-19 17:41
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
@JsonIgnoreProperties(ignoreUnknown = true)
public class ReceiptInfoBean {
    /**
     * 客户名
     */
    @JsonProperty("custom_name")
    private String customName;

    /**
     * 证件号码
     */
    @JsonProperty("card_no")
    private String cardNo;

    /**
     * 手机号
     */
    @JsonProperty("mobile")
    private String mobile;

    /**
     * 贷款金额
     */
    @JsonProperty("apply_amount")
    private BigDecimal applyAmount;

    /**
     * 发放金额
     */
    @JsonProperty("loan_amount")
    private BigDecimal loanAmount;

    /**
     * 期数
     */
    @JsonProperty("period")
    private Integer period;

    /**
     * 当前期数
     */
    @JsonProperty("cur_period")
    private Integer curPeriod;

    /**
     * 还款方式
     * 1：等额本息；
     * 2：等额本金；
     * 3：按月付息到期还本；
     * 4：利随本清；
     * 5：自由还款
     */
    @JsonProperty("repay_type")
    private String repayType;

    /**
     * 还款日
     */
    @JsonProperty("repay_date")
    private String repayDate;

    /**
     * 放款时间
     */
    @JsonDeserialize(using = FlexibleLocalDateTimeDeserializer.class)
    @JsonProperty("loan_time")
    private LocalDateTime loanTime;

    /**
     * 借据状态
     * 0：未还清，
     * 1：已还清
     */
    @JsonProperty("status")
    private String status;

    /**
     * 已还本金
     */
    @JsonProperty("already_corpus")
    private BigDecimal alreadyCorpus;

    /**
     * 已还利息
     */
    @JsonProperty("already_accrual")
    private BigDecimal alreadyAccrual;

    /**
     * 结清日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @JsonProperty("already_date")
    private LocalDate alreadyDate;

    /**
     * 审批状态
     * 0：通过
     * 1：拒绝
     * 2：审批中
     * 3：失败
     */
    @JsonProperty("workflow_status")
    private String workflowStatus;

    /**
     * 借据编号
     */
    @JsonProperty("receipt_no")
    private String receiptNo;

    // Getters and Setters
    public String getCustomName() {
        return customName;
    }

    public void setCustomName(String customName) {
        this.customName = customName;
    }

    public String getCardNo() {
        return cardNo;
    }

    public void setCardNo(String cardNo) {
        this.cardNo = cardNo;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public BigDecimal getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(BigDecimal applyAmount) {
        this.applyAmount = applyAmount;
    }

    public BigDecimal getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(BigDecimal loanAmount) {
        this.loanAmount = loanAmount;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Integer getCurPeriod() {
        return curPeriod;
    }

    public void setCurPeriod(Integer curPeriod) {
        this.curPeriod = curPeriod;
    }

    public String getRepayType() {
        return repayType;
    }

    public void setRepayType(String repayType) {
        this.repayType = repayType;
    }

    public String getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(String repayDate) {
        this.repayDate = repayDate;
    }

    public LocalDateTime getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(LocalDateTime loanTime) {
        this.loanTime = loanTime;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public BigDecimal getAlreadyCorpus() {
        return alreadyCorpus;
    }

    public void setAlreadyCorpus(BigDecimal alreadyCorpus) {
        this.alreadyCorpus = alreadyCorpus;
    }

    public BigDecimal getAlreadyAccrual() {
        return alreadyAccrual;
    }

    public void setAlreadyAccrual(BigDecimal alreadyAccrual) {
        this.alreadyAccrual = alreadyAccrual;
    }

    public LocalDate getAlreadyDate() {
        return alreadyDate;
    }

    public void setAlreadyDate(LocalDate alreadyDate) {
        this.alreadyDate = alreadyDate;
    }

    public String getWorkflowStatus() {
        return workflowStatus;
    }

    public void setWorkflowStatus(String workflowStatus) {
        this.workflowStatus = workflowStatus;
    }

    public String getReceiptNo() {
        return receiptNo;
    }

    public void setReceiptNo(String receiptNo) {
        this.receiptNo = receiptNo;
    }
}

