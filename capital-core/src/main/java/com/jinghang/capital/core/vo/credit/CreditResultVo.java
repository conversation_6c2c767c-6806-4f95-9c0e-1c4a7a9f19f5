package com.jinghang.capital.core.vo.credit;


import com.jinghang.capital.core.vo.StatusAbleVo;

import java.math.BigDecimal;
import java.time.LocalDateTime;

public class CreditResultVo extends StatusAbleVo {
    /**
     * 外部授信id
     */
    private String sysId;
    /**
     * 授信id
     */
    private String creditId;
    /**
     * 授信编号
     */
    private String creditNo;
    /**
     * 授信合同编号
     */
    private String creditContractNo;
    /**
     * 授信申请金额
     */
    private BigDecimal creditAmt;
    /**
     * 授信结果金额
     */
    private BigDecimal creditResultAmt;
    private Integer period;

    private LocalDateTime creditTime;

    private String failMsg;

    private String  bankUserId;
    //outApplSeq
    private String creditSeq;

    public String getCreditSeq() {
        return creditSeq;
    }

    public void setCreditSeq(String creditSeq) {
        this.creditSeq = creditSeq;
    }

    /**
     * 通过时间
     */

    private LocalDateTime passTime;
    /**
     * 资方有效期
     */

    private LocalDateTime capExpireTime;

    public LocalDateTime getPassTime() {
        return passTime;
    }

    public void setPassTime(LocalDateTime passTime) {
        this.passTime = passTime;
    }

    public LocalDateTime getCapExpireTime() {
        return capExpireTime;
    }

    public void setCapExpireTime(LocalDateTime capExpireTime) {
        this.capExpireTime = capExpireTime;
    }

    public String getSysId() {
        return sysId;
    }

    public CreditResultVo setSysId(String sysId) {
        this.sysId = sysId;
        return this;
    }

    public String getCreditId() {
        return creditId;
    }

    public CreditResultVo setCreditId(String creditId) {
        this.creditId = creditId;
        return this;
    }

    public String getCreditNo() {
        return creditNo;
    }

    public void setCreditNo(String creditNo) {
        this.creditNo = creditNo;
    }

    public String getCreditContractNo() {
        return creditContractNo;
    }

    public void setCreditContractNo(String creditContractNo) {
        this.creditContractNo = creditContractNo;
    }

    public BigDecimal getCreditAmt() {
        return creditAmt;
    }

    public CreditResultVo setCreditAmt(BigDecimal creditAmt) {
        this.creditAmt = creditAmt;
        return this;
    }

    public Integer getPeriod() {
        return period;
    }

    public CreditResultVo setPeriod(Integer period) {
        this.period = period;
        return this;
    }

    public String getFailMsg() {
        return failMsg;
    }

    public CreditResultVo setFailMsg(String failMsg) {
        this.failMsg = failMsg;
        return this;
    }

    public LocalDateTime getCreditTime() {
        return creditTime;
    }

    public CreditResultVo setCreditTime(LocalDateTime creditTime) {
        this.creditTime = creditTime;
        return this;
    }

    public BigDecimal getCreditResultAmt() {
        return creditResultAmt;
    }

    public void setCreditResultAmt(BigDecimal creditResultAmt) {
        this.creditResultAmt = creditResultAmt;
    }

    public String getBankUserId() {
        return bankUserId;
    }

    public void setBankUserId(String bankUserId) {
        this.bankUserId = bankUserId;
    }
}
