package com.jinghang.capital.core.vo.recc;

import java.math.BigDecimal;

/**
 * 账单详情
 */
public class ReccDetail {
    /**
     * 对账单流水
     */
    private String cnNo;
    /**
     * 对账类型，见附录-对账单类型
     */
    private String reccType;
    /**
     * 对账类型-转换后的值
     */
    private String reccTypeDesc;

    /**
     * 打款时间
     */
    private String payDate;
    /**
     * 对账单生成日期
     */
    private String createDate;
    /**
     * 借款id
     */
    private String loanId;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款时间
     */
    private String repayTime;

    /**
     * 还款金额
     */
    private BigDecimal amount;
    /**
     * 本金
     */

    private BigDecimal principal;

    /**
     * 利息
     */

    private BigDecimal interest;
    /**
     * 逾期费用
     */
    private BigDecimal overdueFee;
    /**
     * 红冲利息
     */
    private BigDecimal redInterest;
    /**
     * 支付手续费
     */
    private BigDecimal channelFee;
    /**
     * 服务费
     */
    private BigDecimal serviceFee;

    /**
     * 自动对账结果
     */
    private String reccStatus;

    /**
     * 自动对账结果-转换后的值
     */
    private String reccStatusDesc;

    /**
     * 对账单状态
     */
    private String reccResult;
    /**
     * 对账单状态-转换后的值
     */
    private String reccResultDesc;

    public String getCnNo() {
        return cnNo;
    }

    public void setCnNo(String cnNo) {
        this.cnNo = cnNo;
    }

    public String getReccType() {
        return reccType;
    }

    public void setReccType(String reccType) {
        this.reccType = reccType;
    }

    public String getReccTypeDesc() {
        return reccTypeDesc;
    }

    public void setReccTypeDesc(String reccTypeDesc) {
        this.reccTypeDesc = reccTypeDesc;
    }

    public String getPayDate() {
        return payDate;
    }

    public void setPayDate(String payDate) {
        this.payDate = payDate;
    }

    public String getCreateDate() {
        return createDate;
    }

    public void setCreateDate(String createDate) {
        this.createDate = createDate;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public String getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(String repayTime) {
        this.repayTime = repayTime;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getOverdueFee() {
        return overdueFee;
    }

    public void setOverdueFee(BigDecimal overdueFee) {
        this.overdueFee = overdueFee;
    }

    public BigDecimal getRedInterest() {
        return redInterest;
    }

    public void setRedInterest(BigDecimal redInterest) {
        this.redInterest = redInterest;
    }

    public BigDecimal getChannelFee() {
        return channelFee;
    }

    public void setChannelFee(BigDecimal channelFee) {
        this.channelFee = channelFee;
    }

    public BigDecimal getServiceFee() {
        return serviceFee;
    }

    public void setServiceFee(BigDecimal serviceFee) {
        this.serviceFee = serviceFee;
    }

    public String getReccStatus() {
        return reccStatus;
    }

    public void setReccStatus(String reccStatus) {
        this.reccStatus = reccStatus;
    }

    public String getReccStatusDesc() {
        return reccStatusDesc;
    }

    public void setReccStatusDesc(String reccStatusDesc) {
        this.reccStatusDesc = reccStatusDesc;
    }

    public String getReccResult() {
        return reccResult;
    }

    public void setReccResult(String reccResult) {
        this.reccResult = reccResult;
    }

    public String getReccResultDesc() {
        return reccResultDesc;
    }

    public void setReccResultDesc(String reccResultDesc) {
        this.reccResultDesc = reccResultDesc;
    }
}
