package com.jinghang.capital.core.banks.cybk.dto.repay;

import java.math.BigDecimal;

public class CYBKRepayPlanInfo {
    /**
     * 期数
     */
    private Integer perdNo;
    /**
     * 还款日  yyyy-MM-dd
     */
    private String dueDt;
    /**
     * 本金
     */
    private BigDecimal psPrcpAmt;
    /**
     * 正常利息
     */
    private BigDecimal psNormIntAmt;
    /**
     * 逾期利息
     */
    private BigDecimal psOdIntAmt;
    /**
     * 复利
     */
    private BigDecimal psCommOdInt;
    /**
     * 费用金额
     */
    private BigDecimal psFeeAmt;
    /**
     * 已还本金
     */
    private BigDecimal setlPrcp;
    /**
     * 已还利息
     */
    private BigDecimal setlNormInt;
    /**
     * 已还逾期利息
     */
    private BigDecimal setlOdIntAmt;
    /**
     * 已还复利
     */
    private BigDecimal setlCommOdInt;
    /**
     * 已还费用金额
     */
    private BigDecimal setlFeeAmt;
    /**
     * 本金积数
     */
    private BigDecimal prodPrcpAmt;
    /**
     * 利息积数
     */
    private BigDecimal prodIntAmt;
    /**
     * 复利积数
     */
    private BigDecimal prodCommIntAmt;
    /**
     * 逾期标志
     * 当期本金、利息、罚息、复利、费用如果有未结清的，则该标志为'Y'
     */
    private String psOdInd;
    /**
     * 利率
     */
    private BigDecimal intRate;
    /**
     * 罚息利率
     */
    private BigDecimal odIntRate;
    /**
     * 剩余本金
     */
    private BigDecimal psRemPrcp;
    /**
     * 结清标志
     * Y已结清
     * N未结清
     */
    private String setlInd;
    /**
     * 期供金额
     */
    private BigDecimal psInstmAmt;
    /**
     * 当期还款记录是否为主动还款
     * Y是
     * N否
     */
    private String ppErInd;
    /**
     * 最近还款日  yyyy-MM-dd
     */
    private String lastSetlDt;
    /**
     * 是否预收  Y是  N否
     */
    private String intrAcctTx;
    /**
     * 担保费
     */
    private BigDecimal guaraFeeAmt;
    /**
     * 已还担保费
     */
    private BigDecimal setlGuaraFeeAmt;
    /**
     * 应还担保费罚息
     */
    private BigDecimal guaraFeeOdAmt;
    /**
     * 已还担保费罚息
     */
    private BigDecimal setlGuaraFeeOdAmt;

    public Integer getPerdNo() {
        return perdNo;
    }

    public void setPerdNo(Integer perdNo) {
        this.perdNo = perdNo;
    }

    public String getDueDt() {
        return dueDt;
    }

    public void setDueDt(String dueDt) {
        this.dueDt = dueDt;
    }

    public BigDecimal getPsPrcpAmt() {
        return psPrcpAmt;
    }

    public void setPsPrcpAmt(BigDecimal psPrcpAmt) {
        this.psPrcpAmt = psPrcpAmt;
    }

    public BigDecimal getPsNormIntAmt() {
        return psNormIntAmt;
    }

    public void setPsNormIntAmt(BigDecimal psNormIntAmt) {
        this.psNormIntAmt = psNormIntAmt;
    }

    public BigDecimal getPsOdIntAmt() {
        return psOdIntAmt;
    }

    public void setPsOdIntAmt(BigDecimal psOdIntAmt) {
        this.psOdIntAmt = psOdIntAmt;
    }

    public BigDecimal getPsCommOdInt() {
        return psCommOdInt;
    }

    public void setPsCommOdInt(BigDecimal psCommOdInt) {
        this.psCommOdInt = psCommOdInt;
    }

    public BigDecimal getPsFeeAmt() {
        return psFeeAmt;
    }

    public void setPsFeeAmt(BigDecimal psFeeAmt) {
        this.psFeeAmt = psFeeAmt;
    }

    public BigDecimal getSetlPrcp() {
        return setlPrcp;
    }

    public void setSetlPrcp(BigDecimal setlPrcp) {
        this.setlPrcp = setlPrcp;
    }

    public BigDecimal getSetlNormInt() {
        return setlNormInt;
    }

    public void setSetlNormInt(BigDecimal setlNormInt) {
        this.setlNormInt = setlNormInt;
    }

    public BigDecimal getSetlOdIntAmt() {
        return setlOdIntAmt;
    }

    public void setSetlOdIntAmt(BigDecimal setlOdIntAmt) {
        this.setlOdIntAmt = setlOdIntAmt;
    }

    public BigDecimal getSetlCommOdInt() {
        return setlCommOdInt;
    }

    public void setSetlCommOdInt(BigDecimal setlCommOdInt) {
        this.setlCommOdInt = setlCommOdInt;
    }

    public BigDecimal getSetlFeeAmt() {
        return setlFeeAmt;
    }

    public void setSetlFeeAmt(BigDecimal setlFeeAmt) {
        this.setlFeeAmt = setlFeeAmt;
    }

    public BigDecimal getProdPrcpAmt() {
        return prodPrcpAmt;
    }

    public void setProdPrcpAmt(BigDecimal prodPrcpAmt) {
        this.prodPrcpAmt = prodPrcpAmt;
    }

    public BigDecimal getProdIntAmt() {
        return prodIntAmt;
    }

    public void setProdIntAmt(BigDecimal prodIntAmt) {
        this.prodIntAmt = prodIntAmt;
    }

    public BigDecimal getProdCommIntAmt() {
        return prodCommIntAmt;
    }

    public void setProdCommIntAmt(BigDecimal prodCommIntAmt) {
        this.prodCommIntAmt = prodCommIntAmt;
    }

    public String getPsOdInd() {
        return psOdInd;
    }

    public void setPsOdInd(String psOdInd) {
        this.psOdInd = psOdInd;
    }

    public BigDecimal getIntRate() {
        return intRate;
    }

    public void setIntRate(BigDecimal intRate) {
        this.intRate = intRate;
    }

    public BigDecimal getOdIntRate() {
        return odIntRate;
    }

    public void setOdIntRate(BigDecimal odIntRate) {
        this.odIntRate = odIntRate;
    }

    public BigDecimal getPsRemPrcp() {
        return psRemPrcp;
    }

    public void setPsRemPrcp(BigDecimal psRemPrcp) {
        this.psRemPrcp = psRemPrcp;
    }

    public String getSetlInd() {
        return setlInd;
    }

    public void setSetlInd(String setlInd) {
        this.setlInd = setlInd;
    }

    public BigDecimal getPsInstmAmt() {
        return psInstmAmt;
    }

    public void setPsInstmAmt(BigDecimal psInstmAmt) {
        this.psInstmAmt = psInstmAmt;
    }

    public String getPpErInd() {
        return ppErInd;
    }

    public void setPpErInd(String ppErInd) {
        this.ppErInd = ppErInd;
    }

    public String getLastSetlDt() {
        return lastSetlDt;
    }

    public void setLastSetlDt(String lastSetlDt) {
        this.lastSetlDt = lastSetlDt;
    }

    public String getIntrAcctTx() {
        return intrAcctTx;
    }

    public void setIntrAcctTx(String intrAcctTx) {
        this.intrAcctTx = intrAcctTx;
    }

    public BigDecimal getGuaraFeeAmt() {
        return guaraFeeAmt;
    }

    public void setGuaraFeeAmt(BigDecimal guaraFeeAmt) {
        this.guaraFeeAmt = guaraFeeAmt;
    }

    public BigDecimal getSetlGuaraFeeAmt() {
        return setlGuaraFeeAmt;
    }

    public void setSetlGuaraFeeAmt(BigDecimal setlGuaraFeeAmt) {
        this.setlGuaraFeeAmt = setlGuaraFeeAmt;
    }

    public BigDecimal getGuaraFeeOdAmt() {
        return guaraFeeOdAmt;
    }

    public void setGuaraFeeOdAmt(BigDecimal guaraFeeOdAmt) {
        this.guaraFeeOdAmt = guaraFeeOdAmt;
    }

    public BigDecimal getSetlGuaraFeeOdAmt() {
        return setlGuaraFeeOdAmt;
    }

    public void setSetlGuaraFeeOdAmt(BigDecimal setlGuaraFeeOdAmt) {
        this.setlGuaraFeeOdAmt = setlGuaraFeeOdAmt;
    }
}
