package com.jinghang.capital.core.util;

import com.jcraft.jsch.ChannelSftp;

import com.jinghang.common.sftp.DestMapping;
import com.jinghang.common.sftp.Sftp;
import com.jinghang.common.sftp.exception.SftpErrorCode;
import com.jinghang.common.sftp.exception.SftpException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;

/**
 * 文件上传工具类
 */
public class UploadUtil {
    private static final Logger logger = LoggerFactory.getLogger(UploadUtil.class);

    /**
     * 上传文件到sftp
     *
     * @param sftp          需要上传的目标sftp
     * @param localFilePath 需要写入的临时文件
     * @param sftpFilePrefix   上传文件最前的目录（前面包含/）
     * @param sftpDirPath   上传文件目录（对金美信sftp特殊处理）
     * @param sftpFilePath  上传文件名
     */
    public static void uploadJmxZxSftp(Sftp sftp, Path localFilePath, String sftpFilePrefix, String sftpDirPath, String sftpFilePath) {

        try {
            sftp.custom(channel -> {
                try {
                    logger.info("jmx_zx file upload sftp pwd:{}", channel.pwd());
                    mkdir(channel, sftpDirPath);

                    DestMapping mapping = DestMapping.of(sftpFilePrefix + sftpDirPath + sftpFilePath, localFilePath.toFile().getAbsolutePath());
                    channel.put(mapping.getLocalPath(), mapping.getSftpPath());
                } catch (com.jcraft.jsch.SftpException e) {
                    logger.error("jmx_zx file upload sftp error", e);
                    throw new RuntimeException(e);
                }
            });
        } catch (SftpException e) {
            logger.error("jmx_zx file upload sftp error", e);
            throw new RuntimeException(e);
        } finally {
            // 上传文件到sftp
            // uploadFiles(sftp, sftpFilePath, localFilePath.toFile());
            // 删除临时文件
            FileUtil.deletePathFile(localFilePath);
        }
    }

    /**
     * 上传文件到sftp
     *
     * @param sftp          需要上传的目标sftp
     * @param localFilePath 需要写入的临时文件
     * @param sftpFilePrefix   上传文件最前的目录（前面包含/）
     * @param sftpDirPath   上传文件目录（对sftp特殊处理）
     * @param sftpFileName  上传文件名
     */
    public static void uploadFileSftp(Sftp sftp, Path localFilePath, String sftpFilePrefix, String sftpDirPath, String sftpFileName) {

        try {
            sftp.custom(channel -> {
                try {
                    logger.info("file upload sftp pwd:{}", channel.pwd());
                    mkdir(channel, sftpDirPath);

                    DestMapping mapping = DestMapping.of(sftpFilePrefix + sftpDirPath + sftpFileName, localFilePath.toFile().getAbsolutePath());
                    channel.put(mapping.getLocalPath(), mapping.getSftpPath());
                } catch (com.jcraft.jsch.SftpException e) {
                    logger.error("file upload sftp error", e);
                    throw new RuntimeException(e);
                }
            });
        } catch (SftpException e) {
            logger.error("file upload sftp error", e);
            throw new RuntimeException(e);
        } finally {
            // 删除临时文件
            FileUtil.deletePathFile(localFilePath);
        }
    }

    /**
     * 依次创建每层目录
     * @param channel sftp通道
     * @param filePath 文件路径
     * @throws SftpException
     */
    private static void mkdir(ChannelSftp channel, String filePath) throws SftpException {
        String destPath = filePath;
        if (filePath.contains("\\")) {
            destPath = filePath.replaceAll("\\\\", "/");
        }

        String[] paths = destPath.split("/");

        for (int i = 0; i <= paths.length - 1; ++i) {
            String dir = paths[i];
            if (dir != null && !dir.isEmpty()) {
                try {
                    channel.cd(dir);
                } catch (com.jcraft.jsch.SftpException var11) {
                    try {
                        channel.mkdir(dir);
                        channel.cd(dir);
                    } catch (com.jcraft.jsch.SftpException var9) {
                        throw new SftpException(SftpErrorCode.OPERATION, "创建目录[" + dir + "]异常", var9);
                    }
                }
            }
        }
    }


    /**
     * 上传文件到sftp
     *
     * @param sftp         需要上传的目标sftp
     * @param tempFile     需要写入的临时文件
     * @param sftpFilePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, File tempFile, String sftpFilePath) {

        // 上传文件到sftp
        uploadFiles(sftp, sftpFilePath, tempFile);

        // 删除临时文件
        FileUtil.deleteFile(tempFile);
    }

    /**
     * 上传文件到sftp
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要写入的数据
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, String data, String filePath) {
        // 创建临时文件
        File tempFile = FileUtil.createTempFile();

        // 数据写入临时文件
        writeData(tempFile, data);

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        FileUtil.deleteFile(tempFile);
    }

    /**
     * 上传文件到sftp
     *
     * @param sftp     需要上传的目标sftp
     * @param data     需要写入的数据
     * @param filePath 上传文件路径（完整路径，包括文件名及文件类型后缀）
     */
    public static void uploadSftp(Sftp sftp, byte[] data, String filePath) {
        // 创建临时文件
        File tempFile = FileUtil.createTempFile();

        // 数据写入临时文件
        writeData(tempFile, data);

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        FileUtil.deleteFile(tempFile);
    }

    /**
     * Aes加密后文件上传文件到sftp
     *
     * @param sftp         需要上传的目标sftp
     * @param data         需要写入的数据
     * @param filePath     上传文件路径（完整路径，包括文件名及文件类型后缀）
     * @param aesBase64Key 加密文件密钥
     */
    public static void uploadSftp(Sftp sftp, String data, String filePath, String aesBase64Key) {

        // 创建临时文件
        File tempFile = FileUtil.createTempFile();

        // 数据写入临时文件
        writeData(tempFile, data, aesBase64Key);

        // 上传文件到sftp
        uploadFiles(sftp, filePath, tempFile);

        // 删除临时文件
        FileUtil.deleteFile(tempFile);
    }

    /**
     * 将数据写入到临时文件
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, byte[] data) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(data);
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据写入到临时文件
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, String data) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(data.getBytes(StandardCharsets.UTF_8));
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 将数据加密后，写入到临时文件
     *
     * @param tempFile 本地生成的临时文件
     * @param data     需要写入的数据
     */
    private static void writeData(File tempFile, String data, String aesBase64Key) {
        try (FileOutputStream fos = new FileOutputStream(tempFile)) {
            // 将文件流写入本地临时文件
            fos.write(AESUtil.encrypt(data.getBytes(StandardCharsets.UTF_8), aesBase64Key));
        } catch (IOException e) {
            logger.error("文件写入异常", e);
            throw new RuntimeException("文件写入异常", e);
        }
    }

    /**
     * 上传文件到sftp
     *
     * @param sftp         需要上传的目标sftp
     * @param sftpFilePath sftp上传文件路径
     * @param tempFile     本地生成的临时文件
     */
    private static void uploadFiles(Sftp sftp, String sftpFilePath, File tempFile) {
        try {
            sftp.upload(DestMapping.of(sftpFilePath, tempFile.getAbsolutePath()));
        } catch (SftpException e) {
            logger.error("文件上传异常", e);
            throw new RuntimeException("文件上传异常", e);
        }
    }

}

