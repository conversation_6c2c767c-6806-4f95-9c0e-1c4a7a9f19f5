package com.jinghang.capital.core.banks.cybk.dto.bind;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKBindCardApplyResponse {


    /**
     * 外部签约流水号
     */
    private String outSignSeq;
    /**
     * 长银签约流水号
     */
    private String signSeq;
    /**
     * 状态
     * 0:处理中
     * 1:成功
     * 2:失败
     */
    private String status;
    /**
     * 描述
     */
    private String statusDesc;

    public String getOutSignSeq() {
        return outSignSeq;
    }

    public void setOutSignSeq(String outSignSeq) {
        this.outSignSeq = outSignSeq;
    }

    public String getSignSeq() {
        return signSeq;
    }

    public void setSignSeq(String signSeq) {
        this.signSeq = signSeq;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}
