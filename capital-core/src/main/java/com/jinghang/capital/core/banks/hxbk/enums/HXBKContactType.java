package com.jinghang.capital.core.banks.hxbk.enums;

import com.jinghang.capital.core.enums.Relation;

import java.util.Arrays;

/**
 * HXBK联系人关系类型枚举
 *
 * 1配偶——    SPOUSE：配偶
 * 2父母——    PARENTS：父母
 * 3子女——    CHILDREN：子女
 * 4兄弟
 * 5姐妹
 * 6朋友——    FRIEND：朋友
 * 7其他——    RELATIVE：亲戚，   SIBLING：兄弟姐妹，    UNKNOWN:未知，    COLLEAGUE：同事
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 16:00
 */
public enum HXBKContactType {
    PARENTS("2", "父母", Relation.PARENTS),
    SPOUSE("1", "配偶", Relation.SPOUSE),
    SIBLING("7", "兄弟姐妹", Relation.SIBLING),
    FRIEND("6", "朋友", Relation.FRIEND),
    COLLEAGUE("7", "同事", Relation.COLLEAGUE),
    CHILDREN("3", "子女", Relation.CHILDREN),
    RELATIVE("7", "亲戚", Relation.RELATIVE),
    UNKNOWN("7", "其他", Relation.UNKNOWN);

    private final String code;
    private final String desc;
    private final Relation relation;

    HXBKContactType(String code, String desc, Relation relation) {
        this.code = code;
        this.desc = desc;
        this.relation = relation;
    }

    /**
     * 根据通用联系人关系枚举获取HXBK联系人关系枚举
     *
     * @param relation 通用联系人关系枚举
     * @return HXBK联系人关系枚举
     */
    public static HXBKContactType getEnumByRelation(Relation relation) {
        return Arrays.stream(values())
                .filter(hxbkContactType -> relation.equals(hxbkContactType.relation))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 根据HXBK编码获取枚举
     *
     * @param code HXBK联系人关系编码
     * @return HXBK联系人关系枚举
     */
    public static HXBKContactType getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(hxbkContactType -> code.equals(hxbkContactType.code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Relation getRelation() {
        return relation;
    }
}
