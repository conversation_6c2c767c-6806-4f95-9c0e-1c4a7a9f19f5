package com.jinghang.capital.core.banks.cybk.dto.credit;

import java.math.BigDecimal;

public class CYBKGuaranteeInfo {

    /**
     * 担保金额
     */
    private BigDecimal guarAmt;
    /**
     * 担保服务费
     */
    private BigDecimal guarServiceAmt;
    /**
     * 担保费率
     */
    private BigDecimal guarRate;
    /**
     * 担保总期数
     */
    private String guarTime;
    /**
     * 担保罚息利率
     */
    private BigDecimal guarOdIntRate;

    public BigDecimal getGuarAmt() {
        return guarAmt;
    }

    public void setGuarAmt(BigDecimal guarAmt) {
        this.guarAmt = guarAmt;
    }

    public BigDecimal getGuarServiceAmt() {
        return guarServiceAmt;
    }

    public void setGuarServiceAmt(BigDecimal guarServiceAmt) {
        this.guarServiceAmt = guarServiceAmt;
    }

    public BigDecimal getGuarRate() {
        return guarRate;
    }

    public void setGuarRate(BigDecimal guarRate) {
        this.guarRate = guarRate;
    }

    public String getGuarTime() {
        return guarTime;
    }

    public void setGuarTime(String guarTime) {
        this.guarTime = guarTime;
    }

    public BigDecimal getGuarOdIntRate() {
        return guarOdIntRate;
    }

    public void setGuarOdIntRate(BigDecimal guarOdIntRate) {
        this.guarOdIntRate = guarOdIntRate;
    }
}
