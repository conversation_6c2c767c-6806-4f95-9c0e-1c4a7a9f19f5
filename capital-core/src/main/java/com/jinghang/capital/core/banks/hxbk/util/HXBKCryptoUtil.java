package com.jinghang.capital.core.banks.hxbk.util;

import com.jinghang.capital.core.banks.hxbk.config.HXBKConfig;
import com.jinghang.capital.core.banks.hxbk.util.crypto.AESUtils;
import com.jinghang.capital.core.banks.hxbk.util.crypto.ParamUtils;
import com.jinghang.capital.core.banks.hxbk.util.crypto.RSAUtils;
import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.TreeMap;

/**
 * HXBK加解密服务类
 * 封装加解密操作，提供统一的服务接口
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 21:00
 */
@Service
public class HXBKCryptoUtil {

    private static final Logger logger = LoggerFactory.getLogger(HXBKCryptoUtil.class);

    @Autowired
    private HXBKConfig hxbkConfig;

    /**
     * 构建响应数据（用于响应蚂蚁侧请求）
     *
     * @param code    响应码
     * @param msg     响应消息
     * @param bizData 业务数据对象
     * @return 加密签名后的响应JSON字符串
     */
    public String buildEncryptedResponse(String code, String msg, Object bizData) {
        try {
            validateCryptoConfig();

            logger.info("构建HXBK加密响应，code: {}, msg: {}, bizData: {}", code, msg, JsonUtil.toJsonString(bizData));

            TreeMap<String, String> response = new TreeMap<>();
            response.put("code", code);
            response.put("msg", msg);

            String bizDataStr = JsonUtil.toJsonString(bizData);
            String encryptedResponse = ParamUtils.buildResponse(
                    response,
                    bizDataStr,
                    hxbkConfig.getAntPublicKey(),
                    hxbkConfig.getPartnerPrivateKey()
            );

            logger.info("HXBK加密响应构建完成，加密响应内容：{}", encryptedResponse);
            return encryptedResponse;
        } catch (Exception e) {
            logger.error("构建HXBK加密响应失败", e);
            throw new RuntimeException("构建加密响应失败", e);
        }
    }

    /**
     * 构建成功响应
     *
     * @param bizData 业务数据对象
     * @return 加密签名后的成功响应JSON字符串
     */
    public String buildSuccessResponse(Object bizData) {
        return buildEncryptedResponse("000000", "请求成功", bizData);
    }

    /**
     * 构建失败响应
     *
     * @param code 错误码
     * @param msg  错误消息
     * @return 失败响应JSON字符串（不加密）
     */
    public String buildFailResponse(String code, String msg) {
        TreeMap<String, String> response = new TreeMap<>();
        response.put("code", code);
        response.put("msg", msg);
        response.put("responseId", java.util.UUID.randomUUID().toString());
        logger.error("构建HXBK失败响应，response: {}", JsonUtil.toJsonString(response));

        return JsonUtil.toJsonString(response);
    }

    /**
     * 验证加解密配置
     */
    private void validateCryptoConfig() {
        if (StringUtil.isBlank(hxbkConfig.getPartnerPrivateKey())) {
            throw new RuntimeException("合作方私钥未配置");
        }
        if (StringUtil.isBlank(hxbkConfig.getAntPublicKey())) {
            throw new RuntimeException("蚂蚁侧公钥未配置");
        }
    }

    /**
     * 获取蚂蚁侧公钥
     *
     * @return 蚂蚁侧公钥
     */
    public String getAntPublicKey() {
        return hxbkConfig.getAntPublicKey();
    }

    /**
     * 获取合作方私钥
     *
     * @return 合作方私钥
     */
    public String getPartnerPrivateKey() {
        return hxbkConfig.getPartnerPrivateKey();
    }
}
