package com.jinghang.capital.core.banks.cybk.dto.repay;


import com.fasterxml.jackson.annotation.JsonInclude;

import java.math.BigDecimal;


@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKRepayTrialResponse {


    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 还款总金额
     */
    private String totalAmt;
    /**
     * 应还本金
     */
    private String psRemPrcp;
    /**
     * 应还利罚
     * 应还利罚=利息+罚息
     */
    private String odPrcpAmt;
    /**
     * 应还费用
     */
    private String odFeeAmt;
    /**
     * 应还违约金
     */
    private String feeAmt;
    /**
     * 还款授权码
     */
    private String repaymentCode;
    /**
     * 剩余期数（包括本期）
     */
    private String loanOsTnr;
    /**
     * 单笔限额
     */
    private String onceLimitAmt;
    /**
     * 日限额
     */
    private String dayLimitAmt;
    /**
     * 归还担保费
     */
    private BigDecimal guaraFeeAmt;
    /**
     * 归还担保费罚息
     */
    private BigDecimal guaraFeeOdAmt;
    /**
     * 应还利息
     */
    private String intAmt;
    /**
     * 应还罚息
     */
    private String odIntAmt;

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(String totalAmt) {
        this.totalAmt = totalAmt;
    }

    public String getPsRemPrcp() {
        return psRemPrcp;
    }

    public void setPsRemPrcp(String psRemPrcp) {
        this.psRemPrcp = psRemPrcp;
    }

    public String getOdPrcpAmt() {
        return odPrcpAmt;
    }

    public void setOdPrcpAmt(String odPrcpAmt) {
        this.odPrcpAmt = odPrcpAmt;
    }

    public String getOdFeeAmt() {
        return odFeeAmt;
    }

    public void setOdFeeAmt(String odFeeAmt) {
        this.odFeeAmt = odFeeAmt;
    }

    public String getFeeAmt() {
        return feeAmt;
    }

    public void setFeeAmt(String feeAmt) {
        this.feeAmt = feeAmt;
    }

    public String getRepaymentCode() {
        return repaymentCode;
    }

    public void setRepaymentCode(String repaymentCode) {
        this.repaymentCode = repaymentCode;
    }

    public String getLoanOsTnr() {
        return loanOsTnr;
    }

    public void setLoanOsTnr(String loanOsTnr) {
        this.loanOsTnr = loanOsTnr;
    }

    public String getOnceLimitAmt() {
        return onceLimitAmt;
    }

    public void setOnceLimitAmt(String onceLimitAmt) {
        this.onceLimitAmt = onceLimitAmt;
    }

    public String getDayLimitAmt() {
        return dayLimitAmt;
    }

    public void setDayLimitAmt(String dayLimitAmt) {
        this.dayLimitAmt = dayLimitAmt;
    }

    public BigDecimal getGuaraFeeAmt() {
        return guaraFeeAmt;
    }

    public void setGuaraFeeAmt(BigDecimal guaraFeeAmt) {
        this.guaraFeeAmt = guaraFeeAmt;
    }

    public BigDecimal getGuaraFeeOdAmt() {
        return guaraFeeOdAmt;
    }

    public void setGuaraFeeOdAmt(BigDecimal guaraFeeOdAmt) {
        this.guaraFeeOdAmt = guaraFeeOdAmt;
    }

    public String getIntAmt() {
        return intAmt;
    }

    public void setIntAmt(String intAmt) {
        this.intAmt = intAmt;
    }

    public String getOdIntAmt() {
        return odIntAmt;
    }

    public void setOdIntAmt(String odIntAmt) {
        this.odIntAmt = odIntAmt;
    }
}
