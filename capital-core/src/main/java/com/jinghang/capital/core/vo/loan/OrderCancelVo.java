package com.jinghang.capital.core.vo.loan;


import com.jinghang.capital.core.enums.BankChannel;

/**
 * 撤销订单入参数
 */
public class OrderCancelVo {
    /**
     * 资方
     */
    private BankChannel bankChannel;
    /**
     * 进件订单编号，授信id
     */
    private String orderNo;
    /**
     * 先行通申请单编号 资方返回ID
     */
    private String applicationNo;
    /**
     *撤销原因
     */
    private String cancelReason;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getApplicationNo() {
        return applicationNo;
    }

    public void setApplicationNo(String applicationNo) {
        this.applicationNo = applicationNo;
    }

    public String getCancelReason() {
        return cancelReason;
    }

    public void setCancelReason(String cancelReason) {
        this.cancelReason = cancelReason;
    }
}
