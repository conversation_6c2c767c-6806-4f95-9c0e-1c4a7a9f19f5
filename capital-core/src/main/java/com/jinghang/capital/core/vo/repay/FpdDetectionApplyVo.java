package com.jinghang.capital.core.vo.repay;




import com.jinghang.capital.core.enums.BankChannel;

import java.util.List;

public class FpdDetectionApplyVo {

    /**
     * 资方
     */
    private BankChannel channel;

    /**
     * 检测配置
     */
    private List<DetectionConfigVo> configs;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public List<DetectionConfigVo> getConfigs() {
        return configs;
    }

    public void setConfigs(List<DetectionConfigVo> configs) {
        this.configs = configs;
    }
}
