package com.jinghang.capital.core.banks.hxbk.util.crypto;

import com.jinghang.common.util.JsonUtil;
import com.jinghang.common.util.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 参数解析工具类
 * 用于处理HXBK加解密请求响应参数的构建、解析、签名和验签
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 20:00
 */
public class ParamUtils {

    private static final Logger logger = LoggerFactory.getLogger(ParamUtils.class);

    private static final String SUCCESS_CODE = "000000";
    private static final String VERIFY_FAIL_CODE = "100000";
    private static final String DECRYPT_FAIL_CODE = "200000";

    /**
     * 解析请求参数
     *
     * @param bodyJson 请求体JSON对象
     * @return 解析后的请求参数（TreeMap保证排序）
     */
    public static TreeMap<String, String> parseParamFromRequest(Map<String, Object> bodyJson) {
        TreeMap<String, String> paramsToVerify = new TreeMap<>();
        
        paramsToVerify.put("appid", getStringValue(bodyJson, "appid"));
        paramsToVerify.put("method", getStringValue(bodyJson, "method"));
        paramsToVerify.put("version", getStringValue(bodyJson, "version"));
        paramsToVerify.put("timestamp", getStringValue(bodyJson, "timestamp"));
        paramsToVerify.put("signType", getStringValue(bodyJson, "signType"));
        paramsToVerify.put("encrypt", getStringValue(bodyJson, "encrypt"));
        
        String encryptedSecretKey = getStringValue(bodyJson, "secretKey");
        logger.debug("接收到的加密SecretKey长度: {}", encryptedSecretKey != null ? encryptedSecretKey.length() : 0);
        paramsToVerify.put("secretKey", encryptedSecretKey);
        
        String encryptedBizData = getStringValue(bodyJson, "data");
        logger.debug("接收到的业务加密数据长度: {}", encryptedBizData != null ? encryptedBizData.length() : 0);
        paramsToVerify.put("data", encryptedBizData);
        
        paramsToVerify.put("requestId", getStringValue(bodyJson, "requestId"));
        
        return paramsToVerify;
    }

    /**
     * 构建请求参数
     *
     * @param method     请求的接口方法
     * @param bizDataStr 业务数据JSON字符串
     * @param publicKey  对方公钥
     * @param privateKey 己方私钥
     * @return 构建完成的请求JSON字符串
     */
    public static String buildRequest(String method, String bizDataStr, String publicKey, String privateKey) {
        try {
            String secretKey = UUID.randomUUID().toString();
            logger.debug("生成SecretKey: {}", secretKey.substring(0, 8) + "...");

            String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, publicKey);
            logger.debug("RSA加密SecretKey完成，密文长度: {}", encryptedSecretKey.length());

            String encryptedData = AESUtils.encryptDataWithAES(bizDataStr, secretKey);
            logger.debug("AES加密业务数据完成，密文长度: {}", encryptedData.length());

            TreeMap<String, String> params = new TreeMap<>();
            params.put("appid", "dubhe");
            params.put("method", method);
            params.put("version", "1.0");
            params.put("timestamp", String.valueOf(System.currentTimeMillis()));
            params.put("signType", "SHA256WithRSA");
            params.put("encrypt", "1");
            params.put("secretKey", encryptedSecretKey);
            params.put("data", encryptedData);
            params.put("requestId", UUID.randomUUID().toString());

            String toSignStr = params.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(Collectors.joining("&"));
            logger.debug("构建待签名字符串长度: {}", toSignStr.length());

            String signature = RSAUtils.signData(toSignStr, privateKey);
            logger.debug("SHA256WithRSA签名完成，签名长度: {}", signature.length());

            params.put("sign", signature);

            String requestJson = JsonUtil.toJsonString(params);
            logger.info("构建请求参数完成，请求数据长度: {}", requestJson.length());
            return requestJson;
        } catch (Exception e) {
            logger.error("构建请求参数失败", e);
            throw new RuntimeException("构建请求参数失败", e);
        }
    }

    /**
     * 构建返回的业务响应
     *
     * @param response      响应基础数据（包含code、msg等）
     * @param bizResDataStr 业务响应数据JSON字符串
     * @param publicKey     对方公钥
     * @param privateKey    己方私钥
     * @return 构建完成的响应JSON字符串
     */
    public static String buildResponse(TreeMap<String, String> response, String bizResDataStr, 
                                     String publicKey, String privateKey) {
        try {
            String secretKey = UUID.randomUUID().toString();
            logger.debug("生成响应SecretKey: {}", secretKey.substring(0, 8) + "...");

            String encryptedSecretKey = RSAUtils.encryptSecretKeyWithRSA(secretKey, publicKey);
            logger.debug("RSA加密响应SecretKey完成，密文长度: {}", encryptedSecretKey.length());

            String encryptedBizResData = AESUtils.encryptDataWithAES(bizResDataStr, secretKey);
            logger.debug("AES加密响应业务数据完成，密文长度: {}", encryptedBizResData.length());

            response.put("data", encryptedBizResData);
            response.put("encrypt", "1");
            response.put("secretKey", encryptedSecretKey);
            response.put("responseId", UUID.randomUUID().toString());

            String toSignStr = response.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(Collectors.joining("&"));
            logger.debug("构建响应待签名字符串长度: {}", toSignStr.length());

            String responseSignature = RSAUtils.signData(toSignStr, privateKey);
            logger.debug("响应SHA256WithRSA签名完成，签名长度: {}", responseSignature.length());

            response.put("sign", responseSignature);

            String responseJson = JsonUtil.toJsonString(response);
            logger.info("构建响应参数完成，响应数据长度: {}", responseJson.length());
            return responseJson;
        } catch (Exception e) {
            logger.error("构建响应参数失败", e);
            throw new RuntimeException("构建响应参数失败", e);
        }
    }

    /**
     * 验签并解密收到的响应
     *
     * @param responseStr 接收到的响应JSON字符串
     * @param publicKey   对方公钥
     * @param privateKey  己方私钥
     * @return 解密后的响应结果
     */
    @SuppressWarnings("unchecked")
    public static Map<String, Object> verifySignAndDecryptResponse(String responseStr, String publicKey, String privateKey) {
        Map<String, Object> resultJson = new TreeMap<>();
        
        try {
            Map<String, Object> response = JsonUtil.convertToObject(responseStr, Map.class);
            String code = getStringValue(response, "code");
            String msg = getStringValue(response, "msg");
            
            resultJson.put("code", code);
            resultJson.put("msg", msg);

            if (!SUCCESS_CODE.equals(code)) {
                logger.warn("响应异常，code: {}, msg: {}", code, msg);
                return resultJson;
            }

            String respBizData = getStringValue(response, "data");
            String encrypt = getStringValue(response, "encrypt");
            String encryptedSecretKey = getStringValue(response, "secretKey");
            String responseId = getStringValue(response, "responseId");

            logger.debug("响应数据 - 业务数据长度: {}, 加密SecretKey长度: {}", 
                    respBizData != null ? respBizData.length() : 0,
                    encryptedSecretKey != null ? encryptedSecretKey.length() : 0);

            TreeMap<String, String> paramsToVerify = new TreeMap<>();
            paramsToVerify.put("code", code);
            paramsToVerify.put("msg", msg);
            paramsToVerify.put("data", respBizData);
            paramsToVerify.put("encrypt", encrypt);
            paramsToVerify.put("secretKey", encryptedSecretKey);
            paramsToVerify.put("responseId", responseId);

            String toVerifyStr = paramsToVerify.entrySet().stream()
                    .map(e -> e.getKey() + "=" + e.getValue())
                    .collect(Collectors.joining("&"));
            logger.debug("响应数据待验签字符串长度: {}", toVerifyStr.length());

            String signature = getStringValue(response, "sign");
            boolean verified = RSAUtils.verifyData(signature, toVerifyStr, publicKey);
            logger.info("响应数据验签结果: {}", verified);
            
            if (verified) {
                String secretKey = RSAUtils.decryptSecretKeyWithRSA(encryptedSecretKey, privateKey);
                String decryptedResponseData = AESUtils.decryptDataWithAES(respBizData, secretKey);
                logger.info("响应数据解密成功，解密数据长度: {}", decryptedResponseData != null ? decryptedResponseData.length() : 0);
                
                if (StringUtil.isBlank(decryptedResponseData)) {
                    resultJson.put("code", DECRYPT_FAIL_CODE);
                    resultJson.put("msg", "响应数据解密失败");
                    return resultJson;
                } else {
                    Map<String, Object> bizResultJson = JsonUtil.convertToObject(decryptedResponseData, Map.class);
                    resultJson.put("data", bizResultJson);
                    return resultJson;
                }
            } else {
                logger.error("响应验签失败");
                resultJson.put("code", VERIFY_FAIL_CODE);
                resultJson.put("msg", "响应验签失败");
                return resultJson;
            }
        } catch (Exception e) {
            logger.error("验签解密响应失败", e);
            resultJson.put("code", DECRYPT_FAIL_CODE);
            resultJson.put("msg", "验签解密响应异常: " + e.getMessage());
            return resultJson;
        }
    }

    /**
     * 从Map中安全获取字符串值
     *
     * @param map 数据Map
     * @param key 键名
     * @return 字符串值，如果不存在则返回null
     */
    private static String getStringValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        return value != null ? value.toString() : null;
    }
}
