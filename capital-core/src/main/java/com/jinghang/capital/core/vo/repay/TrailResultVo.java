package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.core.enums.Payee;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2023/5/17
 */
public class TrailResultVo {
    private BigDecimal amount;

    private BigDecimal principal;
    private BigDecimal interest;
    private BigDecimal guaranteeFee;
    /**
     * 罚息
     */
    private BigDecimal overdueFee;

    /**
     * 违约金
     */
    private BigDecimal breachFee;

    private Payee payee;

    private BigDecimal haierAmount;

    private BigDecimal haierPrincipal;

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getOverdueFee() {
        return overdueFee;
    }

    public void setOverdueFee(BigDecimal overdueFee) {
        this.overdueFee = overdueFee;
    }

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }

    public Payee getPayee() {
        return payee;
    }

    public void setPayee(Payee payee) {
        this.payee = payee;
    }

    public BigDecimal getHaierAmount() {
        return haierAmount;
    }

    public void setHaierAmount(BigDecimal haierAmount) {
        this.haierAmount = haierAmount;
    }

    public BigDecimal getHaierPrincipal() {
        return haierPrincipal;
    }

    public void setHaierPrincipal(BigDecimal haierPrincipal) {
        this.haierPrincipal = haierPrincipal;
    }
}
