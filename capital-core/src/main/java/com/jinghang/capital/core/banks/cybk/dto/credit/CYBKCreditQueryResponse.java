package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCreditQueryResponse {

    /**
     * 外部授信流水号
     */
    private String outApplSeq;
    /**
     * 长银授信流水号
     */
    private String applCde;
    /**
     * 长银客户号
     */
    private String custId;
    /**
     * 外部状态
     */
    private String outSts;
    /**
     * 授信额度
     */
    private String baseLimit;
    /**
     * 合同编号
     */
    private String contractNo;
    /**
     * 风控对外原因码
     */
    private String outRriskCode;
    /**
     * 风控对外原因码描述
     */
    private String outRiskMsg;
    /**
     * 是否联合放贷
     */
    private String isUnion;
    /**
     * 联合出资方编码
     */
    private String unionChannel;
    /**
     * 联合资方出资金额
     */
    private String unionAmt;
    /**
     * 长银出资金额
     */
    private String centralAmount;
    /**
     * 联合出资总金额
     */
    private String loanAmount;
    /**
     * 联合资金方授信结果
     */
    private String unionStatus;
    /**
     * 担保公司码值
     */
    private String guarCompanyCode;
    /**
     * 担保公司名称
     */
    private String guarCompanyName;

    public String getOutApplSeq() {
        return outApplSeq;
    }

    public void setOutApplSeq(String outApplSeq) {
        this.outApplSeq = outApplSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getCustId() {
        return custId;
    }

    public void setCustId(String custId) {
        this.custId = custId;
    }

    public String getOutSts() {
        return outSts;
    }

    public void setOutSts(String outSts) {
        this.outSts = outSts;
    }

    public String getBaseLimit() {
        return baseLimit;
    }

    public void setBaseLimit(String baseLimit) {
        this.baseLimit = baseLimit;
    }

    public String getContractNo() {
        return contractNo;
    }

    public void setContractNo(String contractNo) {
        this.contractNo = contractNo;
    }

    public String getOutRriskCode() {
        return outRriskCode;
    }

    public void setOutRriskCode(String outRriskCode) {
        this.outRriskCode = outRriskCode;
    }

    public String getOutRiskMsg() {
        return outRiskMsg;
    }

    public void setOutRiskMsg(String outRiskMsg) {
        this.outRiskMsg = outRiskMsg;
    }

    public String getIsUnion() {
        return isUnion;
    }

    public void setIsUnion(String isUnion) {
        this.isUnion = isUnion;
    }

    public String getUnionChannel() {
        return unionChannel;
    }

    public void setUnionChannel(String unionChannel) {
        this.unionChannel = unionChannel;
    }

    public String getUnionAmt() {
        return unionAmt;
    }

    public void setUnionAmt(String unionAmt) {
        this.unionAmt = unionAmt;
    }

    public String getCentralAmount() {
        return centralAmount;
    }

    public void setCentralAmount(String centralAmount) {
        this.centralAmount = centralAmount;
    }

    public String getLoanAmount() {
        return loanAmount;
    }

    public void setLoanAmount(String loanAmount) {
        this.loanAmount = loanAmount;
    }

    public String getUnionStatus() {
        return unionStatus;
    }

    public void setUnionStatus(String unionStatus) {
        this.unionStatus = unionStatus;
    }

    public String getGuarCompanyCode() {
        return guarCompanyCode;
    }

    public void setGuarCompanyCode(String guarCompanyCode) {
        this.guarCompanyCode = guarCompanyCode;
    }

    public String getGuarCompanyName() {
        return guarCompanyName;
    }

    public void setGuarCompanyName(String guarCompanyName) {
        this.guarCompanyName = guarCompanyName;
    }
}
