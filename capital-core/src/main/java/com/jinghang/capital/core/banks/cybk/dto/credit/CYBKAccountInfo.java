package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/10/10 16:52
 **/
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKAccountInfo {

    /**
     * 账号类别
     */
    private String acctKind;
    /**
     * 账号类型
     */
    private String acctTyp;
    /**
     * 账户开户行编码
     */
    private String acctBankCode;
    /**
     * 账号
     */
    private String acctNo;
    /**
     * 账号开户名
     */
    private String acctName;
    /**
     * 开户人证件类型
     */
    private String idTyp;
    /**
     * 开户人证件号码
     */
    private String idNo;
    /**
     * 开户行预留电话
     */
    private String acctPhone;
    /**
     * 预留电话所属地址
     */
    private String wemobileBelongAddr;

    public String getAcctKind() {
        return acctKind;
    }

    public void setAcctKind(String acctKind) {
        this.acctKind = acctKind;
    }

    public String getAcctTyp() {
        return acctTyp;
    }

    public void setAcctTyp(String acctTyp) {
        this.acctTyp = acctTyp;
    }

    public String getAcctBankCode() {
        return acctBankCode;
    }

    public void setAcctBankCode(String acctBankCode) {
        this.acctBankCode = acctBankCode;
    }

    public String getAcctNo() {
        return acctNo;
    }

    public void setAcctNo(String acctNo) {
        this.acctNo = acctNo;
    }

    public String getAcctName() {
        return acctName;
    }

    public void setAcctName(String acctName) {
        this.acctName = acctName;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getAcctPhone() {
        return acctPhone;
    }

    public void setAcctPhone(String acctPhone) {
        this.acctPhone = acctPhone;
    }

    public String getWemobileBelongAddr() {
        return wemobileBelongAddr;
    }

    public void setWemobileBelongAddr(String wemobileBelongAddr) {
        this.wemobileBelongAddr = wemobileBelongAddr;
    }
}
