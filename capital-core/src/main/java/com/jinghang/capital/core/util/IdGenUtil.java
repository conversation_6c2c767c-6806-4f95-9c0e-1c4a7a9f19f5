package com.jinghang.capital.core.util;


import com.jinghang.common.util.DateUtil;
import com.jinghang.common.util.IdGen;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDate;
import java.util.UUID;

public class IdGenUtil {

    private static final int MAX_ID_LENGTH = 32;
    private static final int MAX_ID = 6;

    public static String genReqNo() {
        return IdGen.genId("RN", MAX_ID_LENGTH);
    }

    public static String genReqNo(String perfix) {
        return IdGen.genId(perfix, MAX_ID_LENGTH);
    }
    public static String genReqNo(String perfix, Integer length) {
        return IdGen.genId(perfix, length);
    }
    /**
     * 生成协议编号
     *
     * @param prefix:前缀
     * @param businessId：业务编号
     * @return
     */
    public static String genContractNo(String prefix, String businessId) {
        return prefix + LocalDate.now().format(DateUtil.SHORT_FORMATTER)
            + businessId.substring(Math.max(0, businessId.length() - MAX_ID));
    }



    public static String genUUID() {
        return "RN" + UUID.randomUUID().toString().replace("-", "");
    }

    /**
     * MD5加密方法
     *
     * @param input 需要加密的字符串
     * @return 32位MD5加密后的字符串
     */
    public static String md5Encrypt(String input) {
        if (input == null) {
            return null;
        }

        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] messageDigest = md.digest(input.getBytes());

            StringBuilder hexString = new StringBuilder();
            for (byte b : messageDigest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }

            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5算法不可用", e);
        }
    }


}
