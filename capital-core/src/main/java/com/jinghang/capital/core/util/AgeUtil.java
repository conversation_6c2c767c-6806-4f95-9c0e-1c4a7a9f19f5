package com.jinghang.capital.core.util;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;

/**
 * 年龄计算
 */
public class AgeUtil {

    /**
     * 计算截至今日的周岁.
     *
     * @param birthDay 出生日期
     * @return 周岁
     */
    public static long calcAge(LocalDate birthDay) {
        return calcAge(birthDay, LocalDate.now());
    }

    /**
     * 计算到指定日期的周岁.
     *
     * @param birthDay    出生日期
     * @param expectedDay 指定日期
     * @return 周岁
     */
    public static long calcAge(LocalDate birthDay, LocalDate expectedDay) {
        return Math.abs(ChronoUnit.YEARS.between(birthDay, expectedDay));
    }

}
