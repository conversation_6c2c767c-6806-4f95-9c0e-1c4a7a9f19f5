package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.core.enums.FileType;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2023/5/19
 */
public class PlanPushVo {
    private FileType type;

    private String fileName;
    private LocalDate fileDate;

    private String targetOssBucket;
    private String targetOssKey;

    public FileType getType() {
        return type;
    }

    public void setType(FileType type) {
        this.type = type;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public LocalDate getFileDate() {
        return fileDate;
    }

    public void setFileDate(LocalDate fileDate) {
        this.fileDate = fileDate;
    }

    public String getTargetOssBucket() {
        return targetOssBucket;
    }

    public void setTargetOssBucket(String targetOssBucket) {
        this.targetOssBucket = targetOssBucket;
    }

    public String getTargetOssKey() {
        return targetOssKey;
    }

    public void setTargetOssKey(String targetOssKey) {
        this.targetOssKey = targetOssKey;
    }
}
