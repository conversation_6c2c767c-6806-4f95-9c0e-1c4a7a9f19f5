package com.jinghang.capital.core.banks.cybk.recc;

import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKCustDailyLoanPlan;
import com.jinghang.capital.core.banks.cybk.recc.dto.CYBKLoanReplanDTO;
import com.jinghang.capital.core.service.WarningService;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/8/28 15:37
 * <p>
 * 长银直连 对客日终还款计划文件
 * <p>
 */
@Component
public class CYBKCustDailyLoanPlanHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKCustDailyLoanPlanHandler.class);
    private static final int TWO = 2;
    private static final int SIX = 6;

    @Autowired
    private WarningService warningService;

    @Override
    public void process(LocalDate reccDay) {

        List<CYBKLoanReplanDTO> allList = findCYBKLoanPlanDailyHasChanged(reccDay);

        if (CollectionUtils.isEmpty(allList)) {
            logger.info("日期{} 没有待处理订单", reccDay);
        }

        File sourceFile = null;
        File tempDir = null;
        File okFile = null;
        try {

            Path path = Files.createTempDirectory("CYBK");
            tempDir = path.toFile();

            String filePre = DateFormatUtils.format(new Date(), "yyyyMMdd") + "对客日终还款计划文件";
            sourceFile = File.createTempFile(tempDir.getAbsolutePath() + filePre, ".csv");
            CSVPrinter printer = CSVFormat.DEFAULT.withSkipHeaderRecord().withDelimiter(',').print(sourceFile, StandardCharsets.UTF_8);

            //表头
            // 合作机构贷款唯一编号,长银授信流水号,放款申请流水号,会计日,期次号,起始日期,终止日期,分期状态,结清日期,本金转逾期日期,
            // 利息转逾期日期,本金逾期天数,利息逾期天数,利息余额,本金余额,利息余额,应计非应计标识,罚息余额,应计非应计标识,核销标识
            List<String> header = List.of(
                "loan_seq", "appl_seq", "out_appl_seq", "settle_date", "period", "start_date", "end_date", "status", "clear_date", "prcp_ovd_date",
                "int_ovd_date", "prcp_od_days", "int_ovd_days", "prcp_bal", "int_bal", "od_prcp", "od_prcp_bal", "accrued_status", "write_off");
            printer.printRecord(header);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            //按loanId、期次排序
            allList = allList.stream().sorted(Comparator.comparing(CYBKLoanReplanDTO::getLoanId).thenComparing(CYBKLoanReplanDTO::getPeriod)).toList();
            for (CYBKLoanReplanDTO dto : allList) {
                //转换为日终还款计划对象
                CYBKCustDailyLoanPlan plan = getCsvData(reccDay, dto, sdf);

                List<String> custLoanDTOList = new ArrayList<>();
                custLoanDTOList.add(plan.getLoanSeq());
                custLoanDTOList.add(plan.getApplSeq());
                custLoanDTOList.add(plan.getOutApplSeq()); //授信申请流水号
                custLoanDTOList.add(plan.getSettleDate());
                custLoanDTOList.add(plan.getPeriod().toString());
                custLoanDTOList.add(plan.getStartDate());
                custLoanDTOList.add(plan.getEndDate());
                custLoanDTOList.add(plan.getStatus());
                custLoanDTOList.add(plan.getClearDate());
                custLoanDTOList.add(plan.getPrincipalDate());
                custLoanDTOList.add(plan.getInterestDate());
                custLoanDTOList.add(plan.getOverdueDays());
                custLoanDTOList.add(plan.getOverdueDays());
                custLoanDTOList.add(plan.getPrincipal().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(plan.getInterest().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(plan.getPenalty().movePointRight(TWO).toPlainString());
                custLoanDTOList.add(plan.getPenalty().movePointRight(TWO).toPlainString());
                custLoanDTOList.add("0");
                custLoanDTOList.add("N");
                printer.printRecord(custLoanDTOList);
            }

            printer.close();

            String uploadPath = getCustReccFilePath(reccDay);

            String dateStr = reccDay.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            // /download/cyxf/{产品编码}/out/files/{YYYYMMDD}/eod_repay_plan_daily_${yyyymmdd}.csv
            String fileName = "eod_repay_plan_daily_" + dateStr + ".csv";
            String okFileName = "eod_repay_plan_daily_" + dateStr + ".csv.ok";
            // 上传oss
            logger.info("长银直连上传对客日终还款计划文件");
            getCybkSftpService().upload(uploadPath + fileName, sourceFile.getAbsolutePath().toString());

            // 生成 ok 文件
            Path localVerifyFilePath = Files.createTempFile("loan_" + dateStr, ".csv.ok");
            OutputStream verifyOs = Files.newOutputStream(localVerifyFilePath);
            ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(String.valueOf(allList.size()).getBytes(StandardCharsets.UTF_8));
            IOUtils.copy(byteArrayInputStream, verifyOs);
            getCybkSftpService().upload(uploadPath + okFileName, localVerifyFilePath.toAbsolutePath().toString());
            okFile = localVerifyFilePath.toFile();
        } catch (Exception e) {
            logger.error("长银直连上传对客日终还款计划文件异常", e);
            warningService.warn("长银直连上传对客日终还款计划文件异常");
        } finally {
            if (sourceFile != null) {
                sourceFile.delete();
            }
            if (okFile != null) {
                okFile.delete();
            }
            if (tempDir != null) {
                tempDir.delete();
            }
        }
    }

    @Override
    public ReccType getReccType() {
        return ReccType.CUST_DAILY_LOAN_PLAN;
    }
}
