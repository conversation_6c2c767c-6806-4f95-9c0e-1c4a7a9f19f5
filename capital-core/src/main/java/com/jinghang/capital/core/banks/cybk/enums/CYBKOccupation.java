package com.jinghang.capital.core.banks.cybk.enums;


import com.jinghang.capital.core.enums.Position;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/8/27
 */
public enum CYBKOccupation {
    O0("10000", "国家机关、党群组织、企业、事业单位负责人", Position.NINETEEN),
    O1("20000", "专业技术人员", Position.TWENTY_ONE),
    //O2("30000", "办事人员和有关人员", Position.ELEVEN),
    O3("40000", "商业、服务业人员", Position.EIGHTEEN),
    O4("50000", "农、林、牧、渔、水利业生产人员", Position.TWENTY_FOUR),
    O5("60000", "生产、运输设备操作人员及有关人员", Position.THIRTEEN),
    O6("70000", "军人", Position.TWENTY_TWO),
    O7("80000", "不便分类的其他从业人员", Position.ELEVEN);
    private final String code;
    private final String desc;
    private final Position position;

    CYBKOccupation(String code, String desc, Position position) {
        this.code = code;
        this.desc = desc;
        this.position = position;
    }

    public static String getCYBKCodeByPosition(String position) {
        return Arrays.stream(values()).filter(l -> l.position.name().equals(position)).findFirst().orElse(O7).getCode();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Position getPosition() {
        return position;
    }
}
