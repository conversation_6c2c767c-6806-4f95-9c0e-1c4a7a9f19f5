package com.jinghang.capital.core.banks.cybk.dto.repay;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


public class RepayBankRequest   {
    //长银借据号
    private String dealRefNo;
    //付款人账号
    private String payerAcctNo;
    //付款人姓名
    private String payerName;
    //银行简码
    private String bankCode;
    //银行卡预留手机号
    private String mobile;
    //银行名称
    private String bankName;

    public String getDealRefNo() {
        return dealRefNo;
    }

    public void setDealRefNo(String dealRefNo) {
        this.dealRefNo = dealRefNo;
    }

    public String getPayerAcctNo() {
        return payerAcctNo;
    }

    public void setPayerAcctNo(String payerAcctNo) {
        this.payerAcctNo = payerAcctNo;
    }

    public String getPayerName() {
        return payerName;
    }

    public void setPayerName(String payerName) {
        this.payerName = payerName;
    }

    public String getBankCode() {
        return bankCode;
    }

    public void setBankCode(String bankCode) {
        this.bankCode = bankCode;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getBankName() {
        return bankName;
    }

    public void setBankName(String bankName) {
        this.bankName = bankName;
    }


}
