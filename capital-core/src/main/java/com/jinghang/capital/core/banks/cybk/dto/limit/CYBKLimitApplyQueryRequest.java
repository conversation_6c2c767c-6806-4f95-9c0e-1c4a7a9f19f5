package com.jinghang.capital.core.banks.cybk.dto.limit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;

import java.math.BigDecimal;
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLimitApplyQueryRequest  extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.LIMIT_ADJUST_QUERY;
    /**
     * 长银调额流水号
     */
    private String adjustNo;

    /**
     * 合作方调额流水号
     */
    private String outAdjustNo;

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getOutAdjustNo() {
        return outAdjustNo;
    }

    public void setOutAdjustNo(String outAdjustNo) {
        this.outAdjustNo = outAdjustNo;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
