package com.jinghang.capital.core.util;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.util.Base64;

/**
 * Aes 加解密⼯具类
 */
public class AESUtil {
    private static final String AES_ALGORITHM = "AES";

    /**
     * 加密数据
     *
     * @param data
     * @param aesBase64Key
     * @return
     * @throws Exception
     */
    public static byte[] encrypt(byte[] data, String aesBase64Key) {
        try {
            Cipher cipher = initCipher(aesBase64Key, Cipher.ENCRYPT_MODE);
            return cipher.doFinal(data);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解密数据
     *
     * @param encryptedData
     * @param aesBase64Key
     * @return
     * @throws Exception
     */
    public static byte[] decrypt(byte[] encryptedData, String aesBase64Key) {
        try {
            Cipher cipher = initCipher(aesBase64Key, Cipher.DECRYPT_MODE);
            return cipher.doFinal(encryptedData);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 初始化密码器
     *
     * @param aesBase64Key
     * @param mode
     * @return
     * @throws Exception
     */
    private static Cipher initCipher(String aesBase64Key, int mode) throws Exception {
        SecretKeySpec secretKeySpec = new SecretKeySpec(Base64.getDecoder().decode(aesBase64Key), AES_ALGORITHM);
        Cipher cipher = Cipher.getInstance(AES_ALGORITHM);
        cipher.init(mode, secretKeySpec);
        return cipher;
    }

}

