package com.jinghang.capital.core.banks.hxbk.util.crypto;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;

/**
 * AES加解密业务数据工具类
 * 使用128位AES/ECB/PKCS5Padding对称加密算法
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 20:00
 */
public class AESUtils {

    private static final Logger logger = LoggerFactory.getLogger(AESUtils.class);

    private static final String KEY_ALGORITHM = "AES";
    private static final String DEFAULT_CIPHER_ALGORITHM = "AES/ECB/PKCS5Padding";
    private static final int CACHE_SIZE = 1024;

    /**
     * 使用AES算法加密数据
     *
     * @param content 待加密内容
     * @param key     加密密码
     * @return 返回Base64转码后的加密数据
     */
    public static String encryptDataWithAES(String content, String key) {
        try {
            if (content == null) {
                logger.warn("AES加密内容为空");
                return null;
            }

            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            byte[] byteContent = content.getBytes(StandardCharsets.UTF_8);
            cipher.init(Cipher.ENCRYPT_MODE, getSecretKey(key));
            byte[] result = cipher.doFinal(byteContent);

            String encryptedData = Base64.encodeBase64String(result);
            logger.debug("AES加密成功，原文长度: {}, 密文长度: {}", content.length(), encryptedData.length());
            return encryptedData;
        } catch (Exception e) {
            logger.error("AES加密失败", e);
            throw new RuntimeException("AES加密失败", e);
        }
    }

    /**
     * 使用AES算法解密数据
     *
     * @param content 待解密内容
     * @param key     解密密码
     * @return 解密后的明文
     */
    public static String decryptDataWithAES(String content, String key) {
        try {
            if (content == null) {
                logger.warn("AES解密内容为空");
                return null;
            }

            Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, getSecretKey(key));
            byte[] result = cipher.doFinal(Base64.decodeBase64(content));

            String decryptedData = new String(result, StandardCharsets.UTF_8);
            logger.debug("AES解密成功，密文长度: {}, 明文长度: {}", content.length(), decryptedData.length());
            return decryptedData;
        } catch (Exception e) {
            logger.error("AES解密失败", e);
            throw new RuntimeException("AES解密失败", e);
        }
    }

    /**
     * 生成加密秘钥
     *
     * @param key 原始密钥字符串
     * @return AES密钥规范
     */
    private static SecretKeySpec getSecretKey(final String key) {
        try {
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(key.getBytes());

            KeyGenerator kg = KeyGenerator.getInstance(KEY_ALGORITHM);
            kg.init(128, random);

            SecretKey secretKey = kg.generateKey();
            return new SecretKeySpec(secretKey.getEncoded(), KEY_ALGORITHM);
        } catch (NoSuchAlgorithmException e) {
            logger.error("生成AES密钥失败", e);
            throw new RuntimeException("生成AES密钥失败", e);
        }
    }

    /**
     * <p>
     * 文件加密
     * 蚂蚁上传影像件
     * </p>
     *
     * @param seed
     * @param sourceFilePath
     * @param destFilePath
     * @throws Exception
     */
    public static void encryptFile(String seed, String sourceFilePath, String destFilePath) throws Exception {
        File sourceFile = new File(sourceFilePath);
        File destFile = new File(destFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return;
        }
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }
        destFile.createNewFile();
        try (InputStream in = new FileInputStream(sourceFile)) {
            try (OutputStream out = new FileOutputStream(destFile)) {
                SecretKeySpec secretKeySpec = getSecretKey(seed);
                Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
                cipher.init(Cipher.ENCRYPT_MODE, secretKeySpec);
                try (CipherInputStream cin = new CipherInputStream(in, cipher)) {
                    byte[] cache = new byte[CACHE_SIZE];
                    int nRead = 0;
                    while ((nRead = cin.read(cache)) != -1) {
                        out.write(cache, 0, nRead);
                        out.flush();
                    }
                }
            }
        }

    }

    /**
     * <p>
     * 文件解密
     * 蚂蚁上传影像件
     * </p>
     *
     * @param seed
     * @param sourceFilePath
     * @param destFilePath
     * @throws Exception
     */
    public static void decryptFile(String seed, String sourceFilePath, String destFilePath) throws Exception {
        File sourceFile = new File(sourceFilePath);
        File destFile = new File(destFilePath);
        if (!sourceFile.exists() || !sourceFile.isFile()) {
            return;
        }
        if (!destFile.getParentFile().exists()) {
            destFile.getParentFile().mkdirs();
        }
        destFile.createNewFile();
        try (FileInputStream in = new FileInputStream(sourceFile)) {
            try (FileOutputStream out = new FileOutputStream(destFile)) {
                SecretKeySpec secretKeySpec = getSecretKey(seed);
                Cipher cipher = Cipher.getInstance(DEFAULT_CIPHER_ALGORITHM);
                cipher.init(Cipher.DECRYPT_MODE, secretKeySpec);
                try (CipherOutputStream cipherOutputStream = new CipherOutputStream(out, cipher)) {
                    byte[] cache = new byte[CACHE_SIZE];
                    int nRead = 0;
                    while ((nRead = in.read(cache)) != -1) {
                        cipherOutputStream.write(cache, 0, nRead);
                        cipherOutputStream.flush();
                    }
                }
            }
        }
    }
}
