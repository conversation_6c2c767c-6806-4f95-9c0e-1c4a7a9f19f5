package com.jinghang.capital.core.banks.hxbk.dto.file;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * HXBK结清证明查询响应
 *
 * @Author: Lior
 * @CreateTime: 2025/7/14 14:12
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class HXBKSettlementCertificateResponse {

    /**
     * 结果code
     */
    @JsonProperty("result_code")
    private String resultCode;

    /**
     * 结果信息
     */
    @JsonProperty("result_msg")
    private String resultMsg;

    /**
     * 请求唯一id
     */
    @JsonProperty("req_msg_id")
    private String reqMsgId;

    /**
     * 结清证明信息
     */
    @JsonProperty("certificate_info_list")
    private List<CertificateInfo> certificateInfoList;

    public String getResultCode() {
        return resultCode;
    }

    public void setResultCode(String resultCode) {
        this.resultCode = resultCode;
    }

    public String getResultMsg() {
        return resultMsg;
    }

    public void setResultMsg(String resultMsg) {
        this.resultMsg = resultMsg;
    }

    public String getReqMsgId() {
        return reqMsgId;
    }

    public void setReqMsgId(String reqMsgId) {
        this.reqMsgId = reqMsgId;
    }

    public List<CertificateInfo> getCertificateInfoList() {
        return certificateInfoList;
    }

    public void setCertificateInfoList(List<CertificateInfo> certificateInfoList) {
        this.certificateInfoList = certificateInfoList;
    }

    /**
     * 证明信息
     */
    public static class CertificateInfo {
        /**
         * 结清证明开具结果 0:有结清证明 1:无结清证明 2:开具中 3:暂不支持开具
         */
        @JsonProperty("status")
        private String status;

        /**
         * 说明
         */
        @JsonProperty("message")
        private String message;

        /**
         * 用信申请订单号
         */
        @JsonProperty("relation_no")
        private String relationNo;

        /**
         * 结清证明url
         */
        @JsonProperty("certificate_url")
        private String certificateUrl;

        /**
         * 结清证明文件Base64
         */
        @JsonProperty("certificate_base64")
        private String certificateBase64;

        public String getStatus() {
            return status;
        }

        public void setStatus(String status) {
            this.status = status;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public String getRelationNo() {
            return relationNo;
        }

        public void setRelationNo(String relationNo) {
            this.relationNo = relationNo;
        }

        public String getCertificateUrl() {
            return certificateUrl;
        }

        public void setCertificateUrl(String certificateUrl) {
            this.certificateUrl = certificateUrl;
        }

        public String getCertificateBase64() {
            return certificateBase64;
        }

        public void setCertificateBase64(String certificateBase64) {
            this.certificateBase64 = certificateBase64;
        }
    }
}
