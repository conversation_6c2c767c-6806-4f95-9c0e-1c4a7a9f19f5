package com.jinghang.capital.core.banks.hxbk.enums;

import com.jinghang.capital.core.enums.Education;

import java.util.Arrays;

/**
 * HXBK学历枚举
 * 码值映射关系：
 * 1（研究生或以上）→ 10
 * 2（本科）→ 20
 * 3（大专）→ 30
 * 4（高中）→ 60
 * 6（初中及以下）→ 70
 *
 * @Author: Lior
 * @CreateTime: 2025/7/8 16:00
 */
public enum HXBKEducation {
    MASTER("10", "研究生或以上", Education.MASTER),
    COLLEGE("20", "本科", Education.COLLEGE),
    JUNIOR_COLLEGE("30", "大专", Education.JUNIOR_COLLEGE),
    HIGH_SCHOOL("60", "高中", Education.HIGH_SCHOOL),
    JUNIOR_HIGH_SCHOOL("70", "初中及以下", Education.JUNIOR_HIGH_SCHOOL),
    UNKNOWN("99", "未知", Education.UNKNOWN);

    private final String code;
    private final String desc;
    private final Education education;

    HXBKEducation(String code, String desc, Education education) {
        this.code = code;
        this.desc = desc;
        this.education = education;
    }

    /**
     * 根据通用学历枚举获取HXBK学历枚举
     *
     * @param education 通用学历枚举
     * @return HXBK学历枚举
     */
    public static HXBKEducation getEnumByEducation(Education education) {
        return Arrays.stream(values())
                .filter(hxbkEducation -> education.equals(hxbkEducation.education))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 根据HXBK编码获取枚举
     *
     * @param code HXBK学历编码
     * @return HXBK学历枚举
     */
    public static HXBKEducation getEnumByCode(String code) {
        return Arrays.stream(values())
                .filter(hxbkEducation -> code.equals(hxbkEducation.code))
                .findFirst()
                .orElse(UNKNOWN);
    }

    /**
     * 根据通用学历枚举获取HXBK学历编码
     *
     * @param education 通用学历枚举
     * @return HXBK学历编码
     */
    public static String getCodeByEducation(Education education) {
        return getEnumByEducation(education).getCode();
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public Education getEducation() {
        return education;
    }
}
