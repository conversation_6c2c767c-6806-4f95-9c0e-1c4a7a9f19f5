package com.jinghang.capital.core.banks.cybk.enums;

public enum CYBKCreditStatus {
    PROCESSING("P", "授信中"),
    SUCCESS("S", "授信成功"),
    FAIL("F", "授信失败"),
    REJECT("R", "授信拒绝");
    private final String code;

    private final String desc;

    CYBKCreditStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    CYBKCreditStatus findByCode(String code) {
        CYBKCreditStatus[] values = CYBKCreditStatus.values();
        for (int i = 0; i < values.length; i++) {
            CYBKCreditStatus status = values[i];
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw new RuntimeException("长银直连授信状态未找到");
    }

    public static boolean isRefuse(String code) {
        return FAIL.code.equals(code) || REJECT.code.equals(code);
    }

    public static boolean isProcessing(String code) {
        return PROCESSING.code.equals(code);
    }

    public static boolean isSuccess(String code) {
        return SUCCESS.code.equals(code);
    }
}
