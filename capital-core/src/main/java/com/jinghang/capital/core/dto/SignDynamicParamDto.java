package com.jinghang.capital.core.dto;


import com.jinghang.capital.core.service.agreement.dto.SignCompanyStamp;
import com.jinghang.capital.core.service.agreement.dto.SignPosition;

import java.util.ArrayList;
import java.util.List;

public class SignDynamicParamDto {


    /**
     * 合同名称
     */
    private String contractName;

    private List<SignPosition> personParams = new ArrayList<>();

    private List<SignCompanyStamp> companyParams = new ArrayList<>();


    public List<SignPosition> getPersonParams() {
        return personParams;
    }

    public void setPersonParams(List<SignPosition> personParams) {
        this.personParams = personParams;
    }

    public List<SignCompanyStamp> getCompanyParams() {
        return companyParams;
    }

    public void setCompanyParams(List<SignCompanyStamp> companyParams) {
        this.companyParams = companyParams;
    }

    public String getContractName() {
        return contractName;
    }

    public void setContractName(String contractName) {
        this.contractName = contractName;
    }
}
