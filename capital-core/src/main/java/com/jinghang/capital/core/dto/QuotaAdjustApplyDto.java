package com.jinghang.capital.core.dto;

import java.math.BigDecimal;

public class QuotaAdjustApplyDto {

    private String adjustType;

    private String newLimitStatus;

    private BigDecimal newLimitAmt;

    private String endDate;

    private  String applCde;

    private String creditId;

    private String  adjustNo;

    private String  outAdjustNo;

    private String adjustId;

    public String getAdjustId() {
        return adjustId;
    }

    public void setAdjustId(String adjustId) {
        this.adjustId = adjustId;
    }

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getOutAdjustNo() {
        return outAdjustNo;
    }

    public void setOutAdjustNo(String outAdjustNo) {
        this.outAdjustNo = outAdjustNo;
    }

    public String getAdjustType() {
        return adjustType;
    }

    public void setAdjustType(String adjustType) {
        this.adjustType = adjustType;
    }

    public String getNewLimitStatus() {
        return newLimitStatus;
    }

    public void setNewLimitStatus(String newLimitStatus) {
        this.newLimitStatus = newLimitStatus;
    }

    public BigDecimal getNewLimitAmt() {
        return newLimitAmt;
    }

    public void setNewLimitAmt(BigDecimal newLimitAmt) {
        this.newLimitAmt = newLimitAmt;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }
}
