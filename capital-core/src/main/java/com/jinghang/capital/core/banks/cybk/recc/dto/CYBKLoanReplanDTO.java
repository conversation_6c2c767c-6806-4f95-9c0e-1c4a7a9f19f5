package com.jinghang.capital.core.banks.cybk.recc.dto;


import com.jinghang.capital.core.aspect.JpaQueryMappingDtoAspect;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/28 20:01
 */
@JpaQueryMappingDtoAspect
public class CYBKLoanReplanDTO {

    /**
     * 总期数
     */
    private Integer periods;

    private String accountId;
    private String outerLoanId;

    /**
     * 借据编号
     */
    private String loanNo;


    private String loanId;
    private String creditId;

    /**
     * 期数
     */
    private Integer period;
    /**
     * 应还时间
     */
    private Date repayDate;
    /**
     * 应还总金额
     */
    private BigDecimal totalAmt;
    /**
     * 应还本金
     */
    private BigDecimal principalAmt;
    /**
     * 应还利息
     */
    private BigDecimal interestAmt;
    /**
     * 应还罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;

    /**
     * 实还时间
     */
    private Date actRepayDate;

    private BigDecimal actPenalty;

    private BigDecimal actInterest;

    private BigDecimal actGuarantee;

    private BigDecimal actTotal;

    private String guaranteeContractNo;

    private Date loanTime;

    private BigDecimal loanAmt;

    private Long overdueDays;

    private String custRepayStatus;
    private String bankRepayStatus;

    public String getBankRepayStatus() {
        return bankRepayStatus;
    }

    public void setBankRepayStatus(String bankRepayStatus) {
        this.bankRepayStatus = bankRepayStatus;
    }

    public String getCustRepayStatus() {
        return custRepayStatus;
    }

    public void setCustRepayStatus(String custRepayStatus) {
        this.custRepayStatus = custRepayStatus;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Long getOverdueDays() {
        if (overdueDays == null) {
            return 0L;
        }
        return overdueDays;
    }

    public void setOverdueDays(Long overdueDays) {
        this.overdueDays = overdueDays;
    }

    public Date getLoanTime() {
        return loanTime;
    }

    public void setLoanTime(Date loanTime) {
        this.loanTime = loanTime;
    }

    public String getGuaranteeContractNo() {
        return guaranteeContractNo;
    }

    public void setGuaranteeContractNo(String guaranteeContractNo) {
        this.guaranteeContractNo = guaranteeContractNo;
    }

    public String getCreditId() {
        return creditId;
    }

    public void setCreditId(String creditId) {
        this.creditId = creditId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public Date getRepayDate() {
        return repayDate;
    }

    public void setRepayDate(Date repayDate) {
        this.repayDate = repayDate;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public Date getActRepayDate() {
        return actRepayDate;
    }

    public void setActRepayDate(Date actRepayDate) {
        this.actRepayDate = actRepayDate;
    }

    public BigDecimal getActPenalty() {
        return actPenalty;
    }

    public void setActPenalty(BigDecimal actPenalty) {
        this.actPenalty = actPenalty;
    }

    public BigDecimal getActInterest() {
        return actInterest;
    }

    public void setActInterest(BigDecimal actInterest) {
        this.actInterest = actInterest;
    }

    public BigDecimal getActGuarantee() {
        return actGuarantee;
    }

    public void setActGuarantee(BigDecimal actGuarantee) {
        this.actGuarantee = actGuarantee;
    }

    public BigDecimal getActTotal() {
        return actTotal;
    }

    public void setActTotal(BigDecimal actTotal) {
        this.actTotal = actTotal;
    }
}
