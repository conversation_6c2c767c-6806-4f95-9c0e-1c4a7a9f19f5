package com.jinghang.capital.core.vo.repay;



import com.jinghang.capital.core.enums.RepayPurpose;

import java.math.BigDecimal;

public class ActiveClaimApplyVo {

    /**
     * 放款申请编号
     */
    private String outerLoanId;
    /**
     * 借据号
     */
    private String loanId;
    /**
     * 代偿申请编号
     */
    private String outerClaimId;
    /**
     * 代偿期数
     */
    private Integer period;
    /**
     * 代偿模式（当期、结清）
     */
    private RepayPurpose claimPurpose;
    /**
     * 代偿总金额
     */
    private BigDecimal amount;
    /**
     * 本金
     */
    private BigDecimal principal;
    /**
     * 利息
     */
    private BigDecimal interest;
    /**
     * 罚息
     */
    private BigDecimal penalty;
    /**
     * 融担费
     */
    private BigDecimal guaranteeFee;
    /**
     * 咨询费
     */
    private BigDecimal consultFee;
    /**
     * 违约金
     */
    private BigDecimal breachFee;

    public String getOuterLoanId() {
        return outerLoanId;
    }

    public void setOuterLoanId(String outerLoanId) {
        this.outerLoanId = outerLoanId;
    }

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public String getOuterClaimId() {
        return outerClaimId;
    }

    public void setOuterClaimId(String outerClaimId) {
        this.outerClaimId = outerClaimId;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public RepayPurpose getClaimPurpose() {
        return claimPurpose;
    }

    public void setClaimPurpose(RepayPurpose claimPurpose) {
        this.claimPurpose = claimPurpose;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public BigDecimal getPrincipal() {
        return principal;
    }

    public void setPrincipal(BigDecimal principal) {
        this.principal = principal;
    }

    public BigDecimal getInterest() {
        return interest;
    }

    public void setInterest(BigDecimal interest) {
        this.interest = interest;
    }

    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }

    public BigDecimal getConsultFee() {
        return consultFee;
    }

    public void setConsultFee(BigDecimal consultFee) {
        this.consultFee = consultFee;
    }

    public BigDecimal getBreachFee() {
        return breachFee;
    }

    public void setBreachFee(BigDecimal breachFee) {
        this.breachFee = breachFee;
    }
}
