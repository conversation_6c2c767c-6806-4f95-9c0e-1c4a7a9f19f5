package com.jinghang.capital.core.util;

import java.io.Closeable;
import java.io.IOException;

/**
 * 流工具
 */
public class StreamUtil {

    /**
     * 关闭流
     */
    public static void safeClose(Closeable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (IOException e) {
                // no throw;
            }
        }
    }

}
