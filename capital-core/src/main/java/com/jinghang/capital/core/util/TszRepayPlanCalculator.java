package com.jinghang.capital.core.util;


import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 等额本息360按日计息计算公式
 */
public class TszRepayPlanCalculator {

    /**
     * 一年12月
     */
    protected static final String MONTH = "12";

    /**
     * 利率计算精度
     */
    protected static final int RATE_SCALE = 10;

    /**
     * 金额计算精度
     */
    protected static final int MONEY_SCALE = 2;

    /**
     * 对客每月放款最后一天
     */
    protected static final int CUSTOM_LAST_DAY = 28;


    /**
     * 计算每月还款本息
     *
     * @param loanAmt         借款金额
     * @param capitalLoanRate 对资IRR利率
     * @param periods         总期数
     * @return 还款计划金额
     */
    public static List<TszRepayPlan> calculate(BigDecimal loanAmt, int periods, BigDecimal capitalLoanRate, BigDecimal customDayRate, LocalDate loanDate) {
        List<TszRepayPlan> repayPlans = initPlans(periods);

        Map<Integer, TszRepayPlan> planMap = repayPlans.stream()
                .collect(Collectors.toMap(TszRepayPlan::getPeriod, Function.identity(), (o1, o2) -> o1));

        calculateCapital(planMap, loanAmt, periods, capitalLoanRate, loanDate);

        calculateCustom(planMap, loanAmt, periods, customDayRate, loanDate);

        return repayPlans;
    }

    /**
     * 初始化还款计划
     *
     * @param periods 总期数
     * @return 还款计划
     */
    private static List<TszRepayPlan> initPlans(int periods) {
        List<TszRepayPlan> repays = new ArrayList<>(periods);

        for (int i = 1; i <= periods; i++) {
            TszRepayPlan repay = new TszRepayPlan();
            repay.setPeriod(i);
            repays.add(repay);
        }

        return repays;
    }

    /**
     * 计算对资还款计划
     *
     * @param repayPlanMap 还款计划
     * @param loanAmt      借款金额
     * @param periods      总期数
     * @param loanRate     IRR利率
     * @param loanDate     放款日期
     */
    private static void calculateCapital(Map<Integer, TszRepayPlan> repayPlanMap, final BigDecimal loanAmt, int periods,
                                  final BigDecimal loanRate, final LocalDate loanDate) {
        // 月利率
        final BigDecimal loanMonthRate = loanRate.divide(new BigDecimal(MONTH), RATE_SCALE, RoundingMode.HALF_UP);
        // 借款金额
        // 每月本息金额 = (本金×月利率×(1＋月利率)＾还款月数)÷ ((1＋月利率)＾还款月数-1)
        BigDecimal n = loanMonthRate.add(BigDecimal.ONE).pow(periods); //(1＋月利率)＾还款月数
        BigDecimal monthIncome = loanAmt.multiply(loanMonthRate).multiply(n)
                .divide(n.subtract(BigDecimal.ONE), MONEY_SCALE, RoundingMode.HALF_UP);

        BigDecimal alreadyPrincipal = BigDecimal.ZERO;

        for (int i = 1; i <= periods; i++) {
            TszRepayPlan repay = repayPlanMap.get(i);
            repay.setCapitalRepayDate(loanDate.plusMonths(i));

            // 每月应还利息=贷款本金×月利率×〔(1+月利率)^还款月数-(1+月利率)^(还款月序号-1)〕÷〔(1+月利率)^还款月数-1〕
            BigDecimal monthInterest = loanAmt.multiply(loanMonthRate).multiply(
                            loanMonthRate.add(BigDecimal.ONE).pow(periods).subtract(loanMonthRate.add(BigDecimal.ONE).pow(i - 1)))
                    .divide(loanMonthRate.add(BigDecimal.ONE).pow(periods).subtract(BigDecimal.ONE), MONEY_SCALE, RoundingMode.HALF_UP);

            BigDecimal monthCapital;
            // 归还本金
            if (i == periods) {
                monthCapital = loanAmt.subtract(alreadyPrincipal);
            } else {
                monthCapital = monthIncome.subtract(monthInterest);
                alreadyPrincipal = alreadyPrincipal.add(monthCapital);
            }
            repay.setPrincipalAmt(monthCapital);

            // 归还利息
            repay.setInterestAmt(monthInterest);

            repay.setCapitalTotalAmt(monthCapital.add(monthInterest));
        }
    }


    /**
     * 计算对客还款计划
     *
     * @param repayPlanMap 还款计划
     * @param loanAmt      借款金额
     * @param periods      总期数
     * @param dayRate      日利率
     * @param loanDate     放款日期
     */
    private static void calculateCustom(Map<Integer, TszRepayPlan> repayPlanMap, final BigDecimal loanAmt, int periods,
                                 final BigDecimal dayRate, final LocalDate loanDate) {

        LocalDate firstRepayDate = calcCustomFirstRepayDate(loanDate);

        // 剩余本金
        BigDecimal remainCapital = loanAmt;
        LocalDate preRepayDate = loanDate;

        for (int i = 1; i <= periods; i++) {
            TszRepayPlan repay = repayPlanMap.get(i);
            LocalDate currentRepayDate = firstRepayDate.plusMonths(i - 1);
            repay.setCustomRepayDate(currentRepayDate);
            long between = ChronoUnit.DAYS.between(preRepayDate, currentRepayDate);


            BigDecimal monthInterest = remainCapital.multiply(BigDecimal.valueOf(between)).multiply(dayRate);

            BigDecimal guaranteeAmt = monthInterest.subtract(repay.getInterestAmt());
            repay.setGuaranteeAmt(guaranteeAmt);

            remainCapital = remainCapital.subtract(repay.getPrincipalAmt());

            preRepayDate = currentRepayDate;

            repay.setCustomTotalAmt(repay.getPrincipalAmt().add(repay.getInterestAmt())
                    .add(repay.getGuaranteeAmt()).add(repay.getConsultAmt()));
        }
    }

    /**
     * 计算对客还款计划第1期的还款日
     *
     * @param loanDate 放款日期
     * @return 还款日
     */
    private static LocalDate calcCustomFirstRepayDate(LocalDate loanDate) {
        int dayOfMonth = loanDate.getDayOfMonth();
        if (dayOfMonth > CUSTOM_LAST_DAY) {
            dayOfMonth = CUSTOM_LAST_DAY;
        }
        return loanDate.plusMonths(1L).withDayOfMonth(dayOfMonth);
    }

}
