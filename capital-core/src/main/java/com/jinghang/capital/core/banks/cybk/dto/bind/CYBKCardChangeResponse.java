package com.jinghang.capital.core.banks.cybk.dto.bind;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 银行卡变更 响应
 */
@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCardChangeResponse {

    /**
     * 外部账户变更流水号
     */
    private String outAcctChgSeq;

    /**
     * 长银授信流水号
     */
    private String applCde;

    /**
     * 长银借据号
     */
    private String loanNo;

    /**
     * 状态
     * 0:成功
     * 1:失败
     */
    private String status;
    /**
     * 失败原因描述
     */
    private String statusDesc;

    public String getOutAcctChgSeq() {
        return outAcctChgSeq;
    }

    public void setOutAcctChgSeq(String outAcctChgSeq) {
        this.outAcctChgSeq = outAcctChgSeq;
    }

    public String getApplCde() {
        return applCde;
    }

    public void setApplCde(String applCde) {
        this.applCde = applCde;
    }

    public String getLoanNo() {
        return loanNo;
    }

    public void setLoanNo(String loanNo) {
        this.loanNo = loanNo;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }
}
