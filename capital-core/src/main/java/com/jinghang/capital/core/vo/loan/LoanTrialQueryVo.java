package com.jinghang.capital.core.vo.loan;



import com.jinghang.capital.core.enums.BankChannel;

import java.math.BigDecimal;

/**
 * 借款试算入参
 */
public class LoanTrialQueryVo {

    /**
     * 借款金额
     */
    private BigDecimal loanAmt;

    /**
     * 申请期数
     */
    private Integer periods;

    /**
     * 日利率
     */
    private BigDecimal dayInterestRate;

    /**
     * 年利率
     */
    private BigDecimal yearInterestRate;

    /**
     * 资方
     */
    private BankChannel bankChannel;

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public BigDecimal getDayInterestRate() {
        return dayInterestRate;
    }

    public void setDayInterestRate(BigDecimal dayInterestRate) {
        this.dayInterestRate = dayInterestRate;
    }

    public BigDecimal getYearInterestRate() {
        return yearInterestRate;
    }

    public void setYearInterestRate(BigDecimal yearInterestRate) {
        this.yearInterestRate = yearInterestRate;
    }

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }
}
