package com.jinghang.capital.core.vo.credit;

import java.time.LocalDate;

public class IdCardInfoVo {

    /**
     * 姓名
     */
    private String name;
    /**
     * 民族
     */
    private String nation;
    /**
     * 身份证号
     */
    private String certNo;
    /**
     * 户籍地址
     */
    private String certAddress;


    /**
     * 省行政区划代码
     */
    private String provinceCode;

    /**
     * 市行政区划代码
     */
    private String cityCode;

    /**
     * 区行政区划代码
     */
    private String districtCode;

    /**
     * 发证机关
     */
    private String certSignOrg;
    /**
     * 身份证开始日期
     */
    private LocalDate certValidStart;
    /**
     * 身份证截止日期
     */
    private LocalDate certValidEnd;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getNation() {
        return nation;
    }

    public void setNation(String nation) {
        this.nation = nation;
    }

    public String getCertNo() {
        return certNo;
    }

    public void setCertNo(String certNo) {
        this.certNo = certNo;
    }


    public String getCertAddress() {
        return certAddress;
    }

    public void setCertAddress(String certAddress) {
        this.certAddress = certAddress;
    }

    public String getProvinceCode() {
        return provinceCode;
    }

    public void setProvinceCode(String provinceCode) {
        this.provinceCode = provinceCode;
    }

    public String getCityCode() {
        return cityCode;
    }

    public void setCityCode(String cityCode) {
        this.cityCode = cityCode;
    }

    public String getDistrictCode() {
        return districtCode;
    }

    public void setDistrictCode(String districtCode) {
        this.districtCode = districtCode;
    }

    public String getCertSignOrg() {
        return certSignOrg;
    }

    public void setCertSignOrg(String certSignOrg) {
        this.certSignOrg = certSignOrg;
    }

    public LocalDate getCertValidStart() {
        return certValidStart;
    }

    public void setCertValidStart(LocalDate certValidStart) {
        this.certValidStart = certValidStart;
    }

    public LocalDate getCertValidEnd() {
        return certValidEnd;
    }

    public void setCertValidEnd(LocalDate certValidEnd) {
        this.certValidEnd = certValidEnd;
    }
}
