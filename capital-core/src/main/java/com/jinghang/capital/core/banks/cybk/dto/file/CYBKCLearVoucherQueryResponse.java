package com.jinghang.capital.core.banks.cybk.dto.file;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCLearVoucherQueryResponse {


    /**
     * 状态
     * 0  开具成功
     * 1  开具失败
     * 2  处理中
     */
    private String status;
    /**
     * 失败原因
     */
    private String statusDesc;
    /**
     * 文件路径
     */
    private String imgUrl;
    /**
     * 影像文件ID
     */
    private String imgId;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getImgUrl() {
        return imgUrl;
    }

    public void setImgUrl(String imgUrl) {
        this.imgUrl = imgUrl;
    }

    public String getImgId() {
        return imgId;
    }

    public void setImgId(String imgId) {
        this.imgId = imgId;
    }
}
