package com.jinghang.capital.core.vo.repay;



import com.jinghang.capital.core.entity.BaseEntity;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ProcessStatus;
import com.jinghang.capital.core.enums.RepayMode;
import com.jinghang.capital.core.enums.RepayPurpose;
import com.jinghang.capital.core.enums.RepayType;

import java.math.BigDecimal;
import java.time.LocalDateTime;


public class BankRepayRecordVo extends BaseEntity {
    /**
     * 借款id
     */
    private String loanId;
    /**
     * 资方渠道
     */
    private BankChannel channel;
    /**
     * 期数
     */
    private Integer period;
    /**
     * 还款状态
     */
    private ProcessStatus repayStatus;
    /**
     * 还款时间
     */
    private LocalDateTime repayTime;
    /**
     * 总金额
     */
    private BigDecimal totalAmt;
    /**
     * 本金
     */
    private BigDecimal principalAmt;
    /**
     * 利息
     */
    private BigDecimal interestAmt;
    /**
     * 罚息
     */
    private BigDecimal penaltyAmt;
    /**
     * 平台罚息（对客罚息-对资罚息）
     */
    private BigDecimal platformPenaltyAmt;
    /**
     * 融担费用
     */
    private BigDecimal guaranteeAmt;
    /**
     * 咨询费
     */
    private BigDecimal consultAmt;
    /**
     * 违约金
     */
    private BigDecimal breachAmt;

    /**
     * 还款类型
     */
    private RepayType repayType;
    /**
     * 还款模式
     */
    private RepayMode repayMode;
    /**
     * 还款模式
     */
    private RepayPurpose repayPurpose;

    public String getLoanId() {
        return loanId;
    }

    public void setLoanId(String loanId) {
        this.loanId = loanId;
    }

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public Integer getPeriod() {
        return period;
    }

    public void setPeriod(Integer period) {
        this.period = period;
    }

    public ProcessStatus getRepayStatus() {
        return repayStatus;
    }

    public void setRepayStatus(ProcessStatus repayStatus) {
        this.repayStatus = repayStatus;
    }

    public LocalDateTime getRepayTime() {
        return repayTime;
    }

    public void setRepayTime(LocalDateTime repayTime) {
        this.repayTime = repayTime;
    }

    public BigDecimal getTotalAmt() {
        return totalAmt;
    }

    public void setTotalAmt(BigDecimal totalAmt) {
        this.totalAmt = totalAmt;
    }

    public BigDecimal getPrincipalAmt() {
        return principalAmt;
    }

    public void setPrincipalAmt(BigDecimal principalAmt) {
        this.principalAmt = principalAmt;
    }

    public BigDecimal getInterestAmt() {
        return interestAmt;
    }

    public void setInterestAmt(BigDecimal interestAmt) {
        this.interestAmt = interestAmt;
    }

    public BigDecimal getPenaltyAmt() {
        return penaltyAmt;
    }

    public void setPenaltyAmt(BigDecimal penaltyAmt) {
        this.penaltyAmt = penaltyAmt;
    }

    public BigDecimal getPlatformPenaltyAmt() {
        return platformPenaltyAmt;
    }

    public void setPlatformPenaltyAmt(BigDecimal platformPenaltyAmt) {
        this.platformPenaltyAmt = platformPenaltyAmt;
    }

    public BigDecimal getGuaranteeAmt() {
        return guaranteeAmt;
    }

    public void setGuaranteeAmt(BigDecimal guaranteeAmt) {
        this.guaranteeAmt = guaranteeAmt;
    }

    public BigDecimal getConsultAmt() {
        return consultAmt;
    }

    public void setConsultAmt(BigDecimal consultAmt) {
        this.consultAmt = consultAmt;
    }

    public BigDecimal getBreachAmt() {
        return breachAmt;
    }

    public void setBreachAmt(BigDecimal breachAmt) {
        this.breachAmt = breachAmt;
    }

    public RepayType getRepayType() {
        return repayType;
    }

    public void setRepayType(RepayType repayType) {
        this.repayType = repayType;
    }

    public RepayMode getRepayMode() {
        return repayMode;
    }

    public void setRepayMode(RepayMode repayMode) {
        this.repayMode = repayMode;
    }

    public RepayPurpose getRepayPurpose() {
        return repayPurpose;
    }

    public void setRepayPurpose(RepayPurpose repayPurpose) {
        this.repayPurpose = repayPurpose;
    }
}
