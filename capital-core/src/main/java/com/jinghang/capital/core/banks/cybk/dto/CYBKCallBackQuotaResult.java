package com.jinghang.capital.core.banks.cybk.dto;

import com.jinghang.common.util.JsonUtil;

public class CYBKCallBackQuotaResult {

    private String status;

    private String statusDesc;

    private String adjustNo;

    private String outAdjustNo;

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatusDesc() {
        return statusDesc;
    }

    public void setStatusDesc(String statusDesc) {
        this.statusDesc = statusDesc;
    }

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getOutAdjustNo() {
        return outAdjustNo;
    }

    public void setOutAdjustNo(String outAdjustNo) {
        this.outAdjustNo = outAdjustNo;
    }

    /**
     * 成功返回
     */
    public static String success(String adjustNo,String outAdjustNo) {
        CYBKCallBackQuotaResult result = new CYBKCallBackQuotaResult();
        result.setStatus("01");
        result.setStatusDesc("接收成功");
        result.setAdjustNo(adjustNo);
        result.setOutAdjustNo(outAdjustNo);
        return JsonUtil.toJsonString(result);
    }

    /**
     * 失败返回
     */
    public static String fail(String statusDesc) {
        CYBKCallBackQuotaResult result = new CYBKCallBackQuotaResult();
        result.setStatus("02");
        result.setStatusDesc(statusDesc);
        return JsonUtil.toJsonString(result);
    }

    /**
     * 失败返回
     */
    public static String fail(String adjustNo,String outAdjustNo) {
        CYBKCallBackQuotaResult result = new CYBKCallBackQuotaResult();
        result.setStatus("02");
        result.setStatusDesc("接收失败");
        result.setAdjustNo(adjustNo);
        result.setOutAdjustNo(outAdjustNo);
        return JsonUtil.toJsonString(result);
    }

    /**
     * 失败返回
     */
    public static String fail(String statusDesc,String adjustNo,String outAdjustNo) {
        CYBKCallBackQuotaResult result = new CYBKCallBackQuotaResult();
        result.setStatus("02");
        result.setStatusDesc(statusDesc);
        result.setAdjustNo(adjustNo);
        result.setOutAdjustNo(outAdjustNo);
        return JsonUtil.toJsonString(result);
    }
}
