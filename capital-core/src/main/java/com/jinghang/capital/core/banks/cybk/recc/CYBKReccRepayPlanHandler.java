package com.jinghang.capital.core.banks.cybk.recc;


import com.jinghang.capital.core.banks.cybk.enums.CYBKReccFileType;
import com.jinghang.capital.core.entity.*;
import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.ReccStateEnum;
import com.jinghang.capital.core.vo.recc.ReccType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Component
public class CYBKReccRepayPlanHandler extends CYBKReccAbstractHandler {

    private static final Logger logger = LoggerFactory.getLogger(CYBKReccAbstractHandler.class);

    @Override
    public void process(LocalDate reccDay) {
        //长银还款计划文件
        CYBKReconcileFile reconcileFile = findReconcileFile(reccDay, CYBKReccFileType.REPAYMENT_DETAIL_FILE);
        reconcileFile.setReccDate(LocalDate.now());

        String reccId = reconcileFile.getId();

        // 查到长银的还款成功的还款计划表数据信息
        List<CYBKReccRepayDetail> reccPlans = findReccRepayDetailFileRecords(reccId);

        // 查询本系统的还款计划表数据信息
        List<CustomerLoanReplan> customerLoanReplans = findRepayDetailRecords(BankChannel.CYBK.getCode(),reccDay);

        if (reccPlans.size() == 0 && customerLoanReplans.size() == 0) {
            logger.warn("长银直连对账 还款后还款计划 双方记录都为空, reccDay：[{}]", reccDay);
            reconcileFile.setReccState(ReccStateEnum.S.name());
            updateCYBKReconcileFile(reconcileFile);
            return;
        }
        if (reccPlans.size() != customerLoanReplans.size()) {
            logger.warn("长银直连对账 还款后还款计划 成功条数不一致 reccType：[{}] reccDay：[{}] 业务方条数：[{}] 资方条数：[{}]",
                    CYBKReccFileType.REPAYMENT_FILE, reccDay, customerLoanReplans.size(), reccPlans.size());
            getWarningService().warn("\n长银直连对账:" + CYBKReccFileType.REPAYMENT_FILE + "\n对账日:" + reccDay + "\n 还款后还款计划条数不一致 ");
        }

        List<CYBKReccRepayDetail> successList = new ArrayList<>();

        reccPlans.forEach(rp -> {
            // 拿到loanId
            var loanId = rp.getSysId();
            var existRecord = filterPlanInter(customerLoanReplans, loanId, rp.getPeriod());
            boolean match = match(rp, existRecord);
            rp.setReccStatus(match ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
            if (match) {
                successList.add(rp);
            } else {
                //对账失败打印日志
                warningLog(rp, existRecord, loanId);
            }
            updateReccRepayDetail(rp);
        });
        boolean allMatch = successList.size() == reccPlans.size() && successList.size() == customerLoanReplans.size();
        reconcileFile.setReccState(allMatch ? ReccStateEnum.S.name() : ReccStateEnum.F.name());
        updateCYBKReconcileFile(reconcileFile);
        //对账失败，企业微信告警
        if (ReccStateEnum.F.name().equals(reconcileFile.getReccState())) {
            getWarningService().warn("\n长银直连 还款成功后还款计划 对账失败:" + CYBKReccFileType.PLAN_FILE + "\n对账日:" + reccDay + "\n，对账成功总数与系统还款后还款计划总数不一致 ");
        }
    }

    private List<CustomerLoanReplan> findRepayDetailRecords(String channel, LocalDate reccDay) {
        LocalDateTime startOfDay = reccDay.atStartOfDay();
        LocalDateTime nextDayStart = reccDay.plusDays(1L).atStartOfDay();
        return getCustomerLoanReplanRepository().findByChannelAndCreateDate(channel, startOfDay, nextDayStart);
    }

    private CustomerLoanReplan filterPlanInter(List<CustomerLoanReplan> recordList, String loanId, Integer period) {
        return recordList.stream().filter(record -> record.getLoanId().equals(loanId) && period.equals(record.getPeriod())).findAny().orElse(null);
    }

    private boolean match(CYBKReccRepayDetail recc, CustomerLoanReplan repayRecord) {
        if (repayRecord == null) {
            return false;
        }

        return recc.getActPrincipalAmt().compareTo(repayRecord.getActPrincipalAmt()) == 0
                && recc.getActInterestAmt().compareTo(repayRecord.getActInterestAmt()) == 0
                && recc.getActPenaltyAmt().compareTo(repayRecord.getActPenaltyAmt()) == 0
                && recc.getActGuaranteeFeeAmt().compareTo(repayRecord.getActGuaranteeAmt()) == 0;
    }


    private void warningLog(CYBKReccRepayDetail lf, CustomerLoanReplan existRecord, String loanId) {
        Integer sysPeriod = null;
        if (Objects.nonNull(existRecord)) {
            sysPeriod = existRecord.getPeriod();
        }
        logger.warn("长银直连 还款后还款计划 对账失败，reccType：[{}] 资方loanId：[{}]，还款第[{}]期",
            CYBKReccFileType.REPAYMENT_DETAIL_FILE, loanId, sysPeriod);
    }

    @Override
    public ReccType getReccType() {
        return ReccType.REPAY_PLAN;
    }
}
