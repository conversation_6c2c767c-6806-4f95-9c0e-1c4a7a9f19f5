package com.jinghang.capital.core.vo.recc;




import com.jinghang.capital.core.enums.BankChannel;

import java.time.LocalDate;

/**
 * 对账申请
 */
public class ReccDownloadVo {
    /**
     * 资方
     */
    private BankChannel channel;
    /**
     * 对账文件拉取日期
     */
    private LocalDate pullDate;
    /**
     * 文件类型
     */
    private ReccType reccType;
    /**
     * 是否重写
     *
     * @default: false
     */
    private Boolean isRewriting = false;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public LocalDate getPullDate() {
        return pullDate;
    }

    public void setPullDate(LocalDate pullDate) {
        this.pullDate = pullDate;
    }

    public ReccType getReccType() {
        return reccType;
    }

    public void setReccType(ReccType reccType) {
        this.reccType = reccType;
    }

    public Boolean getRewriting() {
        return isRewriting;
    }

    public void setRewriting(Boolean rewriting) {
        isRewriting = rewriting;
    }
}
