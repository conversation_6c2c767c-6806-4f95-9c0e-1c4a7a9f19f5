package com.jinghang.capital.core.vo.repay;


import com.jinghang.capital.api.dto.GuaranteeCompany;
import com.jinghang.capital.core.enums.BankChannel;

import java.time.LocalDate;

/**
 * 标记资方代偿申请dto
 */
public class ClaimMarkApplyVo {

    private BankChannel channel;

    private GuaranteeCompany guaranteeCompany;

    /**
     * 应还款日
     */
    private LocalDate repayDay;

    public BankChannel getChannel() {
        return channel;
    }

    public void setChannel(BankChannel channel) {
        this.channel = channel;
    }

    public LocalDate getRepayDay() {
        return repayDay;
    }

    public void setRepayDay(LocalDate repayDay) {
        this.repayDay = repayDay;
    }

    public GuaranteeCompany getGuaranteeCompany() {
        return guaranteeCompany;
    }

    public void setGuaranteeCompany(GuaranteeCompany guaranteeCompany) {
        this.guaranteeCompany = guaranteeCompany;
    }
}
