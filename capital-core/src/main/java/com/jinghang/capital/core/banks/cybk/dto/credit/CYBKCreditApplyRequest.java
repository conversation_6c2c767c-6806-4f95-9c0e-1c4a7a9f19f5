package com.jinghang.capital.core.banks.cybk.dto.credit;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.jinghang.capital.core.banks.cybk.dto.CYBKBaseRequest;
import com.jinghang.capital.core.banks.cybk.enums.CYBKTradeCode;


import java.util.List;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKCreditApplyRequest extends CYBKBaseRequest {
    @JsonIgnore
    private static final CYBKTradeCode TRADE_CODE = CYBKTradeCode.CREDIT_APPLY;


    /**
     * 申请人类型
     */
    private String apptTyp;
    /**
     * 申请人名称
     */
    private String custName;
    /**
     * 申请人证件类型（默认送20）
     */
    private String idTyp;
    /**
     * 申请人证件类型为其他备注
     */
    private String idTypOth;
    /**
     * 外部客户号
     */
    private String outCustId;
    /**
     * 断直连标识  Y:是断直连 N:非断直连
     */
    private String directFlag;
    /**
     * 客户归属地
     */
    private String belongAddr;
    /**
     * 申请人证件号
     */
    private String idNo;
    /**
     * 证件有效期起止
     */
    private String idNoStartDate;
    /**
     * 证件有效期截止
     */
    private String idNoEndDate;
    /**
     * 身份证发证机构
     */
    private String idOrgan;
    /**
     * 出生日期
     */
    private String bornDate;
    /**
     * 手机号
     */
    private String indivMobile;
    /**
     * 手机号所属地
     */
    private String mobileBelongAddr;
    /**
     * 邮箱
     */
    private String indivEmail;
    /**
     * 性别
     */
    private String indivSex;
    /**
     * 年龄
     */
    private String apptAge;
    /**
     * 婚姻状况
     */
    private String indivMarital;
    /**
     * 最高学历
     */
    private String indivEdu;
    /**
     * 最高学位
     */
    private String indivDegree;
    /**
     * 毕业学校
     */
    private String graduateSchoolName;

    /**
     * 贷款信息
     */
    private CYBKLoanInfo loanInfo;
    /**
     * 账号信息
     */
    private List<CYBKAccountInfo> accInfoList;
    /**
     * 职业信息
     */
    private CYBKJobInfo occupationInfo;
    /**
     * 家庭信息
     */
    private CYBKFamilyInfo familyInfo;
    /**
     * 配偶信息
     */
    private CYBKSpouseInfo spouseInfo;
    /**
     * 联系人信息
     */
    private List<CYBKRelationInfo> relationList;
    /**
     * 影像信息
     */
    private List<CYBKImageInfo> imageInfoList;
    /**
     * 担保信息
     */
    private CYBKGuaranteeInfo guaranteeInfo;
    /**
     * 设备信息
     */
    private CYBKDeviceInfo deviceInfo;
    /**
     * 扩展信息
     */
    private CYBKExtendInfo extendInfo;

    public String getApptTyp() {
        return apptTyp;
    }

    public void setApptTyp(String apptTyp) {
        this.apptTyp = apptTyp;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getIdTyp() {
        return idTyp;
    }

    public void setIdTyp(String idTyp) {
        this.idTyp = idTyp;
    }

    public String getIdTypOth() {
        return idTypOth;
    }

    public void setIdTypOth(String idTypOth) {
        this.idTypOth = idTypOth;
    }

    public String getOutCustId() {
        return outCustId;
    }

    public void setOutCustId(String outCustId) {
        this.outCustId = outCustId;
    }

    public String getDirectFlag() {
        return directFlag;
    }

    public void setDirectFlag(String directFlag) {
        this.directFlag = directFlag;
    }

    public String getBelongAddr() {
        return belongAddr;
    }

    public void setBelongAddr(String belongAddr) {
        this.belongAddr = belongAddr;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getIdNoStartDate() {
        return idNoStartDate;
    }

    public void setIdNoStartDate(String idNoStartDate) {
        this.idNoStartDate = idNoStartDate;
    }

    public String getIdNoEndDate() {
        return idNoEndDate;
    }

    public void setIdNoEndDate(String idNoEndDate) {
        this.idNoEndDate = idNoEndDate;
    }

    public String getIdOrgan() {
        return idOrgan;
    }

    public void setIdOrgan(String idOrgan) {
        this.idOrgan = idOrgan;
    }

    public String getBornDate() {
        return bornDate;
    }

    public void setBornDate(String bornDate) {
        this.bornDate = bornDate;
    }

    public String getIndivMobile() {
        return indivMobile;
    }

    public void setIndivMobile(String indivMobile) {
        this.indivMobile = indivMobile;
    }

    public String getMobileBelongAddr() {
        return mobileBelongAddr;
    }

    public void setMobileBelongAddr(String mobileBelongAddr) {
        this.mobileBelongAddr = mobileBelongAddr;
    }

    public String getIndivEmail() {
        return indivEmail;
    }

    public void setIndivEmail(String indivEmail) {
        this.indivEmail = indivEmail;
    }

    public String getIndivSex() {
        return indivSex;
    }

    public void setIndivSex(String indivSex) {
        this.indivSex = indivSex;
    }

    public String getApptAge() {
        return apptAge;
    }

    public void setApptAge(String apptAge) {
        this.apptAge = apptAge;
    }

    public String getIndivMarital() {
        return indivMarital;
    }

    public void setIndivMarital(String indivMarital) {
        this.indivMarital = indivMarital;
    }

    public String getIndivEdu() {
        return indivEdu;
    }

    public void setIndivEdu(String indivEdu) {
        this.indivEdu = indivEdu;
    }

    public String getIndivDegree() {
        return indivDegree;
    }

    public void setIndivDegree(String indivDegree) {
        this.indivDegree = indivDegree;
    }

    public String getGraduateSchoolName() {
        return graduateSchoolName;
    }

    public void setGraduateSchoolName(String graduateSchoolName) {
        this.graduateSchoolName = graduateSchoolName;
    }

    public CYBKLoanInfo getLoanInfo() {
        return loanInfo;
    }

    public void setLoanInfo(CYBKLoanInfo loanInfo) {
        this.loanInfo = loanInfo;
    }

    public List<CYBKAccountInfo> getAccInfoList() {
        return accInfoList;
    }

    public void setAccInfoList(List<CYBKAccountInfo> accInfoList) {
        this.accInfoList = accInfoList;
    }

    public CYBKJobInfo getOccupationInfo() {
        return occupationInfo;
    }

    public void setOccupationInfo(CYBKJobInfo occupationInfo) {
        this.occupationInfo = occupationInfo;
    }

    public CYBKFamilyInfo getFamilyInfo() {
        return familyInfo;
    }

    public void setFamilyInfo(CYBKFamilyInfo familyInfo) {
        this.familyInfo = familyInfo;
    }

    public CYBKSpouseInfo getSpouseInfo() {
        return spouseInfo;
    }

    public void setSpouseInfo(CYBKSpouseInfo spouseInfo) {
        this.spouseInfo = spouseInfo;
    }

    public List<CYBKRelationInfo> getRelationList() {
        return relationList;
    }

    public void setRelationList(List<CYBKRelationInfo> relationList) {
        this.relationList = relationList;
    }

    public List<CYBKImageInfo> getImageInfoList() {
        return imageInfoList;
    }

    public void setImageInfoList(List<CYBKImageInfo> imageInfoList) {
        this.imageInfoList = imageInfoList;
    }

    public CYBKGuaranteeInfo getGuaranteeInfo() {
        return guaranteeInfo;
    }

    public void setGuaranteeInfo(CYBKGuaranteeInfo guaranteeInfo) {
        this.guaranteeInfo = guaranteeInfo;
    }

    public CYBKDeviceInfo getDeviceInfo() {
        return deviceInfo;
    }

    public void setDeviceInfo(CYBKDeviceInfo deviceInfo) {
        this.deviceInfo = deviceInfo;
    }

    public CYBKExtendInfo getExtendInfo() {
        return extendInfo;
    }

    public void setExtendInfo(CYBKExtendInfo extendInfo) {
        this.extendInfo = extendInfo;
    }

    @Override
    public CYBKTradeCode getTradeCode() {
        return TRADE_CODE;
    }
}
