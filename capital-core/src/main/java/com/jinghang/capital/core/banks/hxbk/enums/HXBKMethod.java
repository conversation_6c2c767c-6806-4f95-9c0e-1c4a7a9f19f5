package com.jinghang.capital.core.banks.hxbk.enums;

/**
 * 湖消蚂蚁天枢系统方法集
 *
 * @Author: Lior
 * @CreateTime: 2025/7/7 20:16
 */
public enum HXBKMethod {
    /**
     * 2.1 天枢系统资金方代码（资金路由）查询
     */
    FUND_ROUTER_QUERY("riskplus.dubbridge.router.fundrouter.query", "1.0"),
    /**
     * 2.2 天枢系统授信申请接口
     */
    CREDIT_APPLY("riskplus.dubbridge.credit.apply", "1.0"),
    /**
     * 2.3 天枢系统授信额度查询接口
     */
    CREDIT_STATUS_QUERY("riskplus.dubbridge.credit.status.query", "1.0"),
    /**
     * 2.6 天枢系统用信申请接口,即申请放款接口
     */
    LOAN_APPLY("riskplus.dubbridge.usecredit.apply", "1.0"),
    /**
     * 2.7 天枢系统用信结果查询
     */
    LOAN_QUERY("riskplus.dubbridge.usecredit.status.query", "1.0"),
    /**
     * 2.8 天枢系统还款试算接口
     */
    REPAY_TRIAL("riskplus.dubbridge.repay.trial.count", "1.0"),
    /**
     * 2.9 天枢系统还款申请接口
     */
    REPAY_APPLY("riskplus.dubbridge.repay.withhold.repay", "1.0"),
    /**
     * 2.12 天枢系统还款结果查询接口
     */
    REPAY_QUERY("riskplus.dubbridge.repay.result.query", "1.0"),
    /**
     * 3.4 还款计划
     */
    REPAY_LIST_QUERY("riskplus.dubbridge.repay.list.query", "1.0"),
    /**
     * 2.30 开具结清证明查询接口
     */
    SETTLEMENT_CERTIFICATE_QUERY("riskplus.dubbridge.settlement.certificate.query", "1.0"),
    /**
     * 2.17 天枢系统绑定银行卡
     */
    BANKCARD_BIND("riskplus.dubbridge.customer.bankcard.bind", "1.0"),

    /**
     * 3.1 授信申请异步通知
     */
    CREDIT_APPLY_RESULT_NOTIFY("dubhe.credit.apply.result.notify", "1.0"),
    /**
     * 3.2 用信申请异步通知
     */
    LOAN_RESULT_CALLBACK("dubhe.callback.loan.result", "1.0"),
    /**
     * 3.4 还款异步通知V2.0
     */
    REPAY_RESULT_CALLBACK_V2("dubhe.callback.repay.result.v2", "2.0"),

    // 2.16 天枢系统合同获取接口
    CONTRACT_GET("riskplus.dubbridge.search.contract.query", "1.0"),
    ;

    /**
     * 接口名称
     */
    private final String name;

    /**
     * 版本号
     */
    private final String version;

    HXBKMethod(String name, String version) {
        this.name = name;
        this.version = version;
    }

    public String getName() {
        return name;
    }

    public String getVersion() {
        return version;
    }
}
