package com.jinghang.capital.core.banks.cybk.dto.limit;

import com.fasterxml.jackson.annotation.JsonInclude;

@JsonInclude(JsonInclude.Include.NON_EMPTY)
public class CYBKLimitApplyQueryResponse {

    /**
     * 长银调额流水号
     */
    private  String adjustNo;

    /**
     * 合作方调额流水号
     */
    private String outAdjustNo;

    /**
     * 调额结果
     */
    private String adjustStatus;

    /**
     * 调额结果描述
     */
    private String adjustStatusDesc;

    public String getAdjustNo() {
        return adjustNo;
    }

    public void setAdjustNo(String adjustNo) {
        this.adjustNo = adjustNo;
    }

    public String getOutAdjustNo() {
        return outAdjustNo;
    }

    public void setOutAdjustNo(String outAdjustNo) {
        this.outAdjustNo = outAdjustNo;
    }

    public String getAdjustStatus() {
        return adjustStatus;
    }

    public void setAdjustStatus(String adjustStatus) {
        this.adjustStatus = adjustStatus;
    }

    public String getAdjustStatusDesc() {
        return adjustStatusDesc;
    }

    public void setAdjustStatusDesc(String adjustStatusDesc) {
        this.adjustStatusDesc = adjustStatusDesc;
    }
}
