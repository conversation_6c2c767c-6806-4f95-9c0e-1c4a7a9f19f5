package com.jinghang.capital.core.vo.file;



import com.jinghang.capital.core.enums.BankChannel;
import com.jinghang.capital.core.enums.FileType;

import java.math.BigDecimal;

public class PreviewVo {

    /**
     * 资方
     */
    private BankChannel bankChannel;

    /**
     * 用户姓名
     */
    private String custName;

    /**
     * 用户身份证
     */
    private String custCertNo;

    /**
     * 文件类型
     */
    private FileType fileType;

    /**
     * 借款金额
     */
    private BigDecimal loanAmt;

    /**
     * 申请期数
     */

    private Integer periods;

    /**
     * 对资费率
     */
    private BigDecimal bankRate;

    /**
     * 借款用途（中原极易购，资方需求的code）
     */
    private String loanPurpose;

    /**
     * 放款银行卡号
     */
    private String loanCardId;

    /**
     * 还款银行卡号
     */
    private String repayCardId;

    /**
     * 资产方担保费金额
     */
    private BigDecimal guaranteeFee;

    public BankChannel getBankChannel() {
        return bankChannel;
    }

    public void setBankChannel(BankChannel bankChannel) {
        this.bankChannel = bankChannel;
    }

    public String getCustName() {
        return custName;
    }

    public void setCustName(String custName) {
        this.custName = custName;
    }

    public String getCustCertNo() {
        return custCertNo;
    }

    public void setCustCertNo(String custCertNo) {
        this.custCertNo = custCertNo;
    }

    public FileType getFileType() {
        return fileType;
    }

    public void setFileType(FileType fileType) {
        this.fileType = fileType;
    }

    public BigDecimal getLoanAmt() {
        return loanAmt;
    }

    public void setLoanAmt(BigDecimal loanAmt) {
        this.loanAmt = loanAmt;
    }

    public Integer getPeriods() {
        return periods;
    }

    public void setPeriods(Integer periods) {
        this.periods = periods;
    }

    public BigDecimal getBankRate() {
        return bankRate;
    }

    public void setBankRate(BigDecimal bankRate) {
        this.bankRate = bankRate;
    }

    public String getLoanPurpose() {
        return loanPurpose;
    }

    public void setLoanPurpose(String loanPurpose) {
        this.loanPurpose = loanPurpose;
    }

    public String getLoanCardId() {
        return loanCardId;
    }

    public void setLoanCardId(String loanCardId) {
        this.loanCardId = loanCardId;
    }

    public String getRepayCardId() {
        return repayCardId;
    }

    public void setRepayCardId(String repayCardId) {
        this.repayCardId = repayCardId;
    }

    public BigDecimal getGuaranteeFee() {
        return guaranteeFee;
    }

    public void setGuaranteeFee(BigDecimal guaranteeFee) {
        this.guaranteeFee = guaranteeFee;
    }
}
