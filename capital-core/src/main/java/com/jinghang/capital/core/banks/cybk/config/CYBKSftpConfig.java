package com.jinghang.capital.core.banks.cybk.config;


import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CYBKSftpConfig {

    @Value("${cybk.sftp.host}")
    private String cybkSftpHost;

    @Value("${cybk.sftp.port}")
    private Integer cybkSftpPort;

    @Value("${cybk.sftp.password}")
    private String cybkSftpPassword;

    @Value("${cybk.sftp.username}")
    private String cybkSftpUsername;

    @Value("${cybk.sftp.upload.dir}")
    private String cybkSftpUploadDir;

    public String getCybkSftpHost() {
        return cybkSftpHost;
    }

    public Integer getCybkSftpPort() {
        return cybkSftpPort;
    }

    public String getCybkSftpPassword() {
        return cybkSftpPassword;
    }

    public String getCybkSftpUsername() {
        return cybkSftpUsername;
    }

    public String getCybkSftpUploadDir() {
        return cybkSftpUploadDir;
    }
}
