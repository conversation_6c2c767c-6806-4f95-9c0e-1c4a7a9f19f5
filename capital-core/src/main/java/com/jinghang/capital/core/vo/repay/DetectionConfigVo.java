package com.jinghang.capital.core.vo.repay;

import java.math.BigDecimal;

public class DetectionConfigVo {

    /**
     * 检测天数（几天前）
     */
    private Integer fpdDay;
    /**
     * 预警值
     */
    private BigDecimal fpdWarning;

    public Integer getFpdDay() {
        return fpdDay;
    }

    public void setFpdDay(Integer fpdDay) {
        this.fpdDay = fpdDay;
    }

    public BigDecimal getFpdWarning() {
        return fpdWarning;
    }

    public void setFpdWarning(BigDecimal fpdWarning) {
        this.fpdWarning = fpdWarning;
    }
}
