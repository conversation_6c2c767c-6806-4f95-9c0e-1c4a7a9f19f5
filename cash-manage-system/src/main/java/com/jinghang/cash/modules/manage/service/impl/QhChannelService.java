package com.jinghang.cash.modules.manage.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.jinghang.cash.enums.ClientNum;
import com.jinghang.cash.enums.PushType;
import com.jinghang.cash.enums.ResultCode;
import com.jinghang.cash.enums.VersionType;
import com.jinghang.cash.mapper.AppChannelConfigMapper;
import com.jinghang.cash.mapper.AppPushConfigMapper;
import com.jinghang.cash.mapper.AppPushConfigRelationMapper;
import com.jinghang.cash.modules.manage.vo.req.QhChannelRequest;
import com.jinghang.cash.modules.manage.vo.req.QhChannelUpdateRequest;
import com.jinghang.cash.modules.manage.vo.rsp.AppChannelConfigResponse;
import com.jinghang.cash.pojo.AppChannelConfig;
import com.jinghang.cash.pojo.AppPushConfig;
import com.jinghang.cash.pojo.AppPushConfigRelation;
import com.jinghang.cash.service.AppChannelConfigService;
import com.jinghang.cash.service.AppPushConfigRelationService;
import com.jinghang.cash.service.AppPushConfigService;

import com.jinghang.common.util.IdGen;
import com.jinghang.common.util.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR> gale
 * @Classname QhChannelService
 * @Description app渠道配置
 * @Date 2024/3/23 17:47
 */
@Service
public class QhChannelService {
    @Autowired
    private AppChannelConfigService appChannelConfigService;
    @Autowired
    private AppPushConfigRelationService appPushConfigRelationService;

    @Autowired
    private AppChannelConfigMapper appChannelConfigMapper;

    @Autowired
    private AppPushConfigRelationMapper appPushConfigRelationMapper;

    @Autowired
    private AppPushConfigService appPushConfigService;

    @Autowired
    private AppPushConfigMapper appPushConfigMapper;

    private static final Logger logger = LoggerFactory.getLogger(QhChannelService.class);

    public ResultCode add(QhChannelRequest request) {
        logger.info("app渠道配置 新增参数:clientNum {}, channelName:{}, channelNum:{}, versionNum:{}",
            request.getClientNum(), request.getChannelName(), request.getChannelNum(), request.getVersionNum());
        LambdaQueryWrapper<AppChannelConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AppChannelConfig::getChannelNum, request.getChannelNum()).or().eq(AppChannelConfig::getChannelName, request.getChannelName());
        long count = appChannelConfigMapper.selectCount(wrapper);
        if (count > 0) {
            return ResultCode.SAME_DATA;
        }
        ClientNum clientNum = Enum.valueOf(ClientNum.class, request.getClientNum());
        AppChannelConfig appChannelConfig = new AppChannelConfig();
        String appChannelConfigId = IdGen.genId("AC");
        appChannelConfig.setChannelName(request.getChannelName());
        appChannelConfig.setChannelNum(request.getChannelNum());
        appChannelConfig.setClientNum(clientNum);
        appChannelConfig.setId(appChannelConfigId);
        appChannelConfigService.save(appChannelConfig);
        String[] split = request.getVersionNum() == null ? new String[]{} :request.getVersionNum().split(",");
        List<String> stringList = Arrays.stream(split).toList();
        LambdaQueryWrapper<AppPushConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        configLambdaQueryWrapper.eq(AppPushConfig::getClientNum, clientNum);
        List<AppPushConfig> appPushConfigs = appPushConfigMapper.selectList(configLambdaQueryWrapper);
        if (!CollectionUtils.isEmpty(appPushConfigs)) {
            appPushConfigs.forEach(appPushConfig -> {
                AppPushConfigRelation appPushConfigRelation = new AppPushConfigRelation();
                appPushConfigRelation.setPushType(PushType.C);
                appPushConfigRelation.setVersionType(stringList.contains(appPushConfig.getVersionNum())? VersionType.A : VersionType.B);
                appPushConfigRelation.setChannelConfigId(appChannelConfigId);
                appPushConfigRelation.setPushConfigId(appPushConfig.getId());
                appPushConfigRelationService.save(appPushConfigRelation);
            });
        }
        logger.info("app渠道配置 新增完成");
        return ResultCode.SUCCESS;
    }

    public ResultCode update(QhChannelUpdateRequest request) {
        logger.info("app渠道配置 更新参数: request:{}", JsonUtil.toJsonString(request));
        long count = appChannelConfigService.count(new LambdaQueryWrapper<AppChannelConfig>()
            .ne(AppChannelConfig::getId, request.getAppChannelId()).eq(AppChannelConfig::getChannelNum, request.getChannelNum())
            .or().ne(AppChannelConfig::getId, request.getAppChannelId()).eq(AppChannelConfig::getChannelName, request.getChannelName()));
        if (count > 0) {
            return ResultCode.SAME_DATA;
        }
        ClientNum clientNum = Enum.valueOf(ClientNum.class, request.getClientNum());
        AppChannelConfig appChannelConfig = new AppChannelConfig();
        appChannelConfig.setId(request.getAppChannelId());
        appChannelConfig.setChannelName(request.getChannelName());
        appChannelConfig.setChannelNum(request.getChannelNum());
        appChannelConfig.setClientNum(clientNum);
        appChannelConfigService.updateById(appChannelConfig);

        String[] split = request.getVersionNum() == null ? new String[]{} :request.getVersionNum().split(",");
        List<String> stringList = Arrays.stream(split).toList();

        LambdaQueryWrapper<AppPushConfig> configLambdaQueryWrapper = new LambdaQueryWrapper<>();
        configLambdaQueryWrapper.eq(AppPushConfig::getClientNum, clientNum);
        List<AppPushConfig> appPushConfigs = appPushConfigMapper.selectList(configLambdaQueryWrapper);

        List<AppPushConfigRelation> appPushConfigRelations = appPushConfigRelationService
            .list(new LambdaQueryWrapper<AppPushConfigRelation>().eq(AppPushConfigRelation::getChannelConfigId, request.getAppChannelId()));
        if (appPushConfigs.size() != appPushConfigRelations.size()) {
            Map<String, AppPushConfigRelation> stringAppPushConfigRelationMap = appPushConfigRelations.stream().collect(Collectors.toMap(AppPushConfigRelation::getPushConfigId, Function.identity()));
            if (!CollectionUtils.isEmpty(appPushConfigs)) {
                appPushConfigs.forEach(appPushConfig -> {
                    if (stringAppPushConfigRelationMap.containsKey(appPushConfig.getId())) {
                        AppPushConfigRelation appPushConfigRelation = stringAppPushConfigRelationMap.get(appPushConfig.getId());
                        appPushConfigRelation.setPushType(PushType.C);
                        appPushConfigRelation.setVersionType(stringList.contains(appPushConfig.getVersionNum())? VersionType.A : VersionType.B);
                        appPushConfigRelation.setChannelConfigId(request.getAppChannelId());
                        appPushConfigRelation.setPushConfigId(appPushConfig.getId());
                        appPushConfigRelationService.updateById(appPushConfigRelation);
                    } else {
                        AppPushConfigRelation appPushConfigRelation = new AppPushConfigRelation();
                        appPushConfigRelation.setPushType(PushType.C);
                        appPushConfigRelation.setVersionType(stringList.contains(appPushConfig.getVersionNum())? VersionType.A : VersionType.B);
                        appPushConfigRelation.setChannelConfigId(request.getAppChannelId());
                        appPushConfigRelation.setPushConfigId(appPushConfig.getId());
                        appPushConfigRelationService.save(appPushConfigRelation);
                    }
                });
            }
        } else {
            Map<String, String> appPushConfigMap = appPushConfigs.stream().collect(Collectors.toMap(AppPushConfig::getId, AppPushConfig::getVersionNum));
            appPushConfigRelations.forEach(appPushConfigRelation -> {
                if (stringList.contains(appPushConfigMap.get(appPushConfigRelation.getPushConfigId()))) {
                    appPushConfigRelation.setVersionType(VersionType.A);
                } else {
                    appPushConfigRelation.setVersionType(VersionType.B);
                }
                appPushConfigRelationService.updateById(appPushConfigRelation);
            });
        }
        logger.info("app渠道配置 更新完成");
        return ResultCode.SUCCESS;
    }

    public PageInfo<AppChannelConfigResponse> queryChannelList(Integer pageNum, Integer pageSize) {
        logger.info("app更新版本服务类调用-查询渠道列表");
        PageHelper.startPage(pageNum, pageSize);
        List<AppChannelConfig> list = appChannelConfigService.list();
        PageInfo<AppChannelConfig> page = new PageInfo<>(list);
        List<AppChannelConfigResponse> responses = new ArrayList<>();
        list.forEach(appChannelConfig -> {
            AppChannelConfigResponse appChannelConfigResponse = new AppChannelConfigResponse();
            appChannelConfigResponse.setAppChannelId(appChannelConfig.getId());
            appChannelConfigResponse.setChannelName(appChannelConfig.getChannelName());
            appChannelConfigResponse.setChannelNum(appChannelConfig.getChannelNum());
            appChannelConfigResponse.setClientNum(appChannelConfig.getClientNum().name());
            appChannelConfigResponse.setVersionNum(appPushConfigRelationService.list(new LambdaQueryWrapper<AppPushConfigRelation>()
                .eq(AppPushConfigRelation::getChannelConfigId, appChannelConfig.getId()))
                .stream()
                .filter(e -> e.getVersionType() == VersionType.A)
                .map(appPushConfigRelation -> {
                    String pushConfigId = appPushConfigRelation.getPushConfigId();
                    return appPushConfigMapper.selectById(pushConfigId).getVersionNum();
                })
                .collect(Collectors.joining(",")));
            responses.add(appChannelConfigResponse);
        });
        PageInfo<AppChannelConfigResponse> pageInfo = new PageInfo<>(responses);
        pageInfo.setTotal(page.getTotal());
        pageInfo.setPages(page.getPages());
        pageInfo.setPageNum(page.getPageNum());
        pageInfo.setPageSize(page.getPageSize());
        logger.info("app更新版本服务类调用-查询渠道列表成功");
        return pageInfo;
    }
}
